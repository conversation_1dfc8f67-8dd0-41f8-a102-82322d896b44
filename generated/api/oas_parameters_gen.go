// Code generated by ogen, DO NOT EDIT.

package api

// DeleteInternalPatientProfilesParams is parameters of delete-internal-patient-profiles operation.
type DeleteInternalPatientProfilesParams struct {
	PatientID string
}

// DeletePatientProfileParams is parameters of delete-patient-profile operation.
type DeletePatientProfileParams struct {
	PatientId string
}

// DeleteTransfersTransferIdParams is parameters of delete-transfers-transferId operation.
type DeleteTransfersTransferIdParams struct {
	UploadSessionId OptString
	TransferId      string
}

// DeleteUploadRequestStudyV1Params is parameters of delete-upload-request-study-v1 operation.
type DeleteUploadRequestStudyV1Params struct {
	// The id of the study to delete from this upload request.
	ID string
}

// DeleteV2HealthrecordsPatientIdRecordsRecordIdParams is parameters of delete-v2-healthrecords-patientId-records-recordId operation.
type DeleteV2HealthrecordsPatientIdRecordsRecordIdParams struct {
	PatientId string
	RecordId  string
}

// DeleteV2TransfersTransferIdParams is parameters of delete-v2-transfers-transferId operation.
type DeleteV2TransfersTransferIdParams struct {
	UploadSessionID OptString
	TransferId      string
}

// GetImageByIDParams is parameters of get-image-byID operation.
type GetImageByIDParams struct {
	// Tell the API what format to return the image.
	Accept GetImageByIDAccept
	// Authentication token for image access.
	XImageToken OptString
	ImageID     string
}

// GetImageMetadataByIDParams is parameters of get-image-metadata-byID operation.
type GetImageMetadataByIDParams struct {
	Imageid string
}

// GetOrganVisualizationByExamIDParams is parameters of get-organ-visualization-by-exam-id operation.
type GetOrganVisualizationByExamIDParams struct {
	ExamId string
}

// GetPatientProfilesIDParams is parameters of get-patient-profiles-id operation.
type GetPatientProfilesIDParams struct {
	PatientId string
}

// GetPhysicianAccountEunityTokenParams is parameters of get-physician-account-eunity-token operation.
type GetPhysicianAccountEunityTokenParams struct {
	ShareID string
}

// GetPhysicianAccountPatientsExamsParams is parameters of get-physician-account-patients-exams operation.
type GetPhysicianAccountPatientsExamsParams struct {
	PatientID string
}

// GetProviderByIdParams is parameters of get-provider-byId operation.
type GetProviderByIdParams struct {
	ProviderId int
}

// GetProviderDetailsV1Params is parameters of get-provider-details-v1 operation.
type GetProviderDetailsV1Params struct {
	// The subdomain associated with the provider.
	Subdomain string
}

// GetProviderFormConfigParams is parameters of get-provider-formConfig operation.
type GetProviderFormConfigParams struct {
	ProviderId int
}

// GetProvidersParams is parameters of get-providers operation.
type GetProvidersParams struct {
	SearchTerm OptString
}

// GetProvidersConsentsConsentidParams is parameters of get-providers-consents-consentid operation.
type GetProvidersConsentsConsentidParams struct {
	Consentid string
}

// GetProvidersConsentsConsentidTypeParams is parameters of get-providers-consents-consentid-type operation.
type GetProvidersConsentsConsentidTypeParams struct {
	ConsentId string
}

// GetProvidersConsentsConsentidUnverifiedParams is parameters of get-providers-consents-consentid-unverified operation.
type GetProvidersConsentsConsentidUnverifiedParams struct {
	Consentid string
}

// GetReferTokenParams is parameters of get-refer-token operation.
type GetReferTokenParams struct {
	Token string
}

// GetReportByIdParams is parameters of get-report-byId operation.
type GetReportByIdParams struct {
	// Tell the API what format to return the report.
	Accept   GetReportByIdAccept
	ReportId string
}

// GetReportsReportIdInsightsQuestionsParams is parameters of get-reports-reportId-insights-questions operation.
type GetReportsReportIdInsightsQuestionsParams struct {
	ReportId string
}

// GetReportsReportIdMetadataParams is parameters of get-reports-reportId-metadata operation.
type GetReportsReportIdMetadataParams struct {
	ReportId string
}

// GetShareExamsByshareIdV2Params is parameters of get-share-exams-byshareId-v2 operation.
type GetShareExamsByshareIdV2Params struct {
	ShareId string
}

// GetSharesParams is parameters of get-shares operation.
type GetSharesParams struct {
	// For pagination.
	Limit OptInt
	// For pagination.
	Offset OptInt
}

// GetSharesByIdParams is parameters of get-shares-byId operation.
type GetSharesByIdParams struct {
	// Comma separated list of exam ids to download. ignored for json option.
	Eids    OptString
	ShareId string
}

// GetSharesHealthrecordsByIdParams is parameters of get-shares-healthrecords-byId operation.
type GetSharesHealthrecordsByIdParams struct {
	// The FHIR ID's to get data for in a comma separated list.
	Hrids   OptString
	ShareId string
}

// GetShortUrlsParams is parameters of get-short-urls operation.
type GetShortUrlsParams struct {
	Slug string
}

// GetTransfersTransferIdParams is parameters of get-transfers-transferId operation.
type GetTransfersTransferIdParams struct {
	TransferId string
}

// GetTransfersTransferIdChallengeParams is parameters of get-transfers-transferId-challenge operation.
type GetTransfersTransferIdChallengeParams struct {
	TransferId string
}

// GetUploadRequestStudyByIDV1Params is parameters of get-upload-request-study-by-id-v1 operation.
type GetUploadRequestStudyByIDV1Params struct {
	// The id of the study to get from this upload request.
	ID string
}

// GetUserExamByIdParams is parameters of get-user-exam-byId operation.
type GetUserExamByIdParams struct {
	ExamId string
}

// GetUserExamsParams is parameters of get-user-exams operation.
type GetUserExamsParams struct {
	// Filter exams by whether or not they are activated.
	Activated OptBool
	// Include reports in response.
	IncludeReports OptBool
	// Filter by UID.
	UID OptString
	// Filter by account ID.
	AccountID OptString
}

// GetUserNotificationsParams is parameters of get-user-notifications operation.
type GetUserNotificationsParams struct {
	IncludeRead OptBool
}

// GetUserRequestConsentParams is parameters of get-user-request-consent operation.
type GetUserRequestConsentParams struct {
	RequestID  OptString
	ProviderID OptInt
	TransferID OptString
}

// GetUsersExamsLookupParams is parameters of get-users-exams-lookup operation.
type GetUsersExamsLookupParams struct {
	// Sso token.
	Token OptString
	// Accession number.
	Accession OptString
}

// GetUsersRolloutFlagParams is parameters of get-users-rollout-flag operation.
type GetUsersRolloutFlagParams struct {
	// Flag of rollout, either 'resetpw' or 'login'.
	Flag string
}

// GetV1AppointmentsParams is parameters of get-v1-appointments operation.
type GetV1AppointmentsParams struct {
	// Legacy ID of provider.
	ProviderID int64
	// Consent ID.
	ConsentID string
}

// GetV1ExpertreviewPaymentprovidersParams is parameters of get-v1-expertreview-paymentproviders operation.
type GetV1ExpertreviewPaymentprovidersParams struct {
	// Available providers in the country.
	Country OptString
}

// GetV1FeaturesIDAuthorizeParams is parameters of get-v1-features-id-authorize operation.
type GetV1FeaturesIDAuthorizeParams struct {
	UserID OptFloat64
	OrgID  OptFloat64
	ID     int
}

// GetV1PlansParams is parameters of get-v1-plans operation.
type GetV1PlansParams struct {
	Recurring OptBool
}

// GetV1PromoCodeParams is parameters of get-v1-promo-code operation.
type GetV1PromoCodeParams struct {
	Code string
}

// GetV1ReportsIDTaggedhtmlParams is parameters of get-v1-reports-id-taggedhtml operation.
type GetV1ReportsIDTaggedhtmlParams struct {
	ID string
}

// GetV1ReportsReportIdInsightsFollowupParams is parameters of get-v1-reports-reportId-insights-followup operation.
type GetV1ReportsReportIdInsightsFollowupParams struct {
	ReportId string
}

// GetV1RequestsIncompleteIncompleteRequestIdParams is parameters of get-v1-requests-incomplete-incompleteRequestId operation.
type GetV1RequestsIncompleteIncompleteRequestIdParams struct {
	IncompleteRequestId string
}

// GetV1RequestsIncompleteIncompleteRequestIdEmailParams is parameters of get-v1-requests-incomplete-incompleteRequestId-email operation.
type GetV1RequestsIncompleteIncompleteRequestIdEmailParams struct {
	IncompleteRequestId string
}

// GetV1SecondopinionEligibleExamIdParams is parameters of get-v1-secondopinion-eligible-examId operation.
type GetV1SecondopinionEligibleExamIdParams struct {
	ExamId string
}

// GetV2HealthrecordsMychartSearchParams is parameters of get-v2-healthrecords-mychart-search operation.
type GetV2HealthrecordsMychartSearchParams struct {
	Query string
}

// GetV2HealthrecordsPatientIdRecordsRecordIdParams is parameters of get-v2-healthrecords-patientId-records-recordId operation.
type GetV2HealthrecordsPatientIdRecordsRecordIdParams struct {
	PatientId string
	RecordId  string
}

// GetV2HealthrecordsPatientIdRecordsRecordIdThumbnailParams is parameters of get-v2-healthrecords-patientId-records-recordId-thumbnail operation.
type GetV2HealthrecordsPatientIdRecordsRecordIdThumbnailParams struct {
	PatientId string
	RecordId  string
}

// GetV2HealthrecordsPatientIdRecordsRecordTypeParams is parameters of get-v2-healthrecords-patientId-records-recordType operation.
type GetV2HealthrecordsPatientIdRecordsRecordTypeParams struct {
	PatientId  string
	RecordType string
	// Max amount of results to return.
	Limit OptString
}

// GetV2HealthrecordsPatientIdUploadParams is parameters of get-v2-healthrecords-patientId-upload operation.
type GetV2HealthrecordsPatientIdUploadParams struct {
	// PH Patient ID of health record being uploaded.
	PatientId string
}

// GetV2PatientIdParams is parameters of get-v2-patientId operation.
type GetV2PatientIdParams struct {
	// If set to true, an email is sent to the user prompting them to refer others to upload their
	// vaccine record.
	CovidVaccine OptBool
	PatientId    string
}

// GetV2ProvidersParams is parameters of get-v2-providers operation.
type GetV2ProvidersParams struct {
	ID string
}

// GetV2RhoEligibleExamIdParams is parameters of get-v2-rho-eligible-examId operation.
type GetV2RhoEligibleExamIdParams struct {
	ExamId string
}

// GetV2SecondopiniontDoctorsSearchParams is parameters of get-v2-secondopiniont-doctors-search operation.
type GetV2SecondopiniontDoctorsSearchParams struct {
	// Search term.
	Query OptString
}

// GetV2UsersExamsLookupParams is parameters of get-v2-users-exams-lookup operation.
type GetV2UsersExamsLookupParams struct {
	// Sso token.
	Token OptString
	// Accession number.
	Accession OptString
}

// PatchPatientProfileParams is parameters of patch-patient-profile operation.
type PatchPatientProfileParams struct {
	PatientId string
}

// PatchRequestsIDCancelParams is parameters of patch-requests-id-cancel operation.
type PatchRequestsIDCancelParams struct {
	ID string
}

// PatchV1RequestsIncompleteIncompleteRequestIdParams is parameters of patch-v1-requests-incomplete-incompleteRequestId operation.
type PatchV1RequestsIncompleteIncompleteRequestIdParams struct {
	IncompleteRequestId string
}

// PostPhysicianLicenseParams is parameters of post-physician-license operation.
type PostPhysicianLicenseParams struct {
	PhysicianID string
}

// PostProvidersConsentsConsentidParams is parameters of post-providers-consents-consentid operation.
type PostProvidersConsentsConsentidParams struct {
	Consentid string
}

// PostProvidersConsentsConsentidUnverifiedParams is parameters of post-providers-consents-consentid-unverified operation.
type PostProvidersConsentsConsentidUnverifiedParams struct {
	Consentid string
}

// PostProvidersConsentsEmailVerificationParams is parameters of post-providers-consents-email-verification operation.
type PostProvidersConsentsEmailVerificationParams struct {
	Consentid string
}

// PostReferTokenParams is parameters of post-refer-token operation.
type PostReferTokenParams struct {
	Token string
}

// PostReportViewsParams is parameters of post-report-views operation.
type PostReportViewsParams struct {
	ReportId string
}

// PostRequestsIDRejectVerifyParams is parameters of post-requests-id-rejectVerify operation.
type PostRequestsIDRejectVerifyParams struct {
	ID string
}

// PostRequestsIDResubmitParams is parameters of post-requests-id-resubmit operation.
type PostRequestsIDResubmitParams struct {
	ID string
}

// PostReshareParams is parameters of post-reshare operation.
type PostReshareParams struct {
	ShareId string
}

// PostTransferPaymentParams is parameters of post-transfer-payment operation.
type PostTransferPaymentParams struct {
	TransferId string
}

// PostTransfersTransferIdChallengeParams is parameters of post-transfers-transferId-challenge operation.
type PostTransfersTransferIdChallengeParams struct {
	TransferId string
}

// PostTransfersTransferIdReactivateParams is parameters of post-transfers-transferId-reactivate operation.
type PostTransfersTransferIdReactivateParams struct {
	TransferId string
}

// PostUserNotificationParams is parameters of post-user-notification operation.
type PostUserNotificationParams struct {
	IncludeRead OptBool
}

// PostV1RequestsIncompleteIncompleteRequestIdEmailStatusParams is parameters of post-v1-requests-incomplete-incompleteRequestId-email-status operation.
type PostV1RequestsIncompleteIncompleteRequestIdEmailStatusParams struct {
	IncompleteRequestId string
}

// PostV1RequestsIncompleteIncompleteRequestIdVerifyParams is parameters of post-v1-requests-incomplete-incompleteRequestId-verify operation.
type PostV1RequestsIncompleteIncompleteRequestIdVerifyParams struct {
	IncompleteRequestId string
}

// PostV1SecondopinionReviewsParams is parameters of post-v1-secondopinion-reviews operation.
type PostV1SecondopinionReviewsParams struct {
	Examid string
}

// PostV1SharesShareIdDltokenParams is parameters of post-v1-shares-shareId-dltoken operation.
type PostV1SharesShareIdDltokenParams struct {
	ShareId string
}

// PostV2HealthrecordsMychartPatientIdParams is parameters of post-v2-healthrecords-mychart-patientId operation.
type PostV2HealthrecordsMychartPatientIdParams struct {
	PatientId string
}

// PostV2HealthrecordsPatientIdParams is parameters of post-v2-healthrecords-patientId operation.
type PostV2HealthrecordsPatientIdParams struct {
	PatientId string
}

// PostV2HealthrecordsPatientIdGailParams is parameters of post-v2-healthrecords-patientId-gail operation.
type PostV2HealthrecordsPatientIdGailParams struct {
	PatientId string
}

// PostV2OrdersParams is parameters of post-v2-orders operation.
type PostV2OrdersParams struct {
	// A reason the new subscription is being created; may be useful for analytics.
	Reason OptString
}

// PostV2RhoRequestParams is parameters of post-v2-rho-request operation.
type PostV2RhoRequestParams struct {
	ExamId string
}

// PostV2TransfersTransferIdFileParams is parameters of post-v2-transfers-transferId-file operation.
type PostV2TransfersTransferIdFileParams struct {
	UploadSessionID OptString
	TransferId      string
}

// PostV2TransfersTransferIdFinalizeParams is parameters of post-v2-transfers-transferId-finalize operation.
type PostV2TransfersTransferIdFinalizeParams struct {
	UploadSessionID OptString
	TransferId      string
}

// PostV2TransfersTransferIdReportdcmParams is parameters of post-v2-transfers-transferId-reportdcm operation.
type PostV2TransfersTransferIdReportdcmParams struct {
	UploadSessionID OptString
	TransferId      string
}

// PutNotificationReadByIdParams is parameters of put-notification-read-byId operation.
type PutNotificationReadByIdParams struct {
	NotificationId string
}

// PutPhysicianAccountsShareExtendParams is parameters of put-physician-accounts-share-extend operation.
type PutPhysicianAccountsShareExtendParams struct {
	ShareID string
}

// PutRevokeShareParams is parameters of put-revoke-share operation.
type PutRevokeShareParams struct {
	ShareId string
}

// PutShareExtendByshareIdParams is parameters of put-share-extend-byshareId operation.
type PutShareExtendByshareIdParams struct {
	ShareId string
}

// PutV2HealthrecordsPatientIdRecordsRecordIdParams is parameters of put-v2-healthrecords-patientId-records-recordId operation.
type PutV2HealthrecordsPatientIdRecordsRecordIdParams struct {
	PatientId string
	RecordId  string
}

// UpdateUploadRequestStudyV1Params is parameters of update-upload-request-study-v1 operation.
type UpdateUploadRequestStudyV1Params struct {
	// The id of the study to update within this upload request.
	ID string
}

// V1MeddreamGenerateExamUUIDGetParams is parameters of GET /v1/meddream/generate/{exam_uuid} operation.
type V1MeddreamGenerateExamUUIDGetParams struct {
	ExamUUID string
}

// V1RecordsUploadStatusGetParams is parameters of GET /v1/records/upload-status operation.
type V1RecordsUploadStatusGetParams struct {
	AccountID string
}

// V1UsersExamsEligibleInsightsGetParams is parameters of GET /v1/users/exams/eligible-insights operation.
type V1UsersExamsEligibleInsightsGetParams struct {
	PatientID OptString
}

// V1UsersExamsExamuuidThumbnailGetParams is parameters of GET /v1/users/exams/{examuuid}/thumbnail operation.
type V1UsersExamsExamuuidThumbnailGetParams struct {
	Examuuid string
}

// V1UsersNotificationsNotificationIdDeleteParams is parameters of DELETE /v1/users/notifications/{notificationId} operation.
type V1UsersNotificationsNotificationIdDeleteParams struct {
	NotificationId string
}

// V1UsersReportinsightsGetParams is parameters of GET /v1/users/reportinsights operation.
type V1UsersReportinsightsGetParams struct {
	PatientID OptString
}

// V2PatientsPatientIDValidGetParams is parameters of GET /v2/patients/{patient_id}/valid operation.
type V2PatientsPatientIDValidGetParams struct {
	PatientID string
}

// V2RequestsRidStatusHistoryPostParams is parameters of POST /v2/requests/{rid}/status/history operation.
type V2RequestsRidStatusHistoryPostParams struct {
	Rid string
}

// V2SecondopinionPatientEligibilityPatientIdProgramNamePutParams is parameters of PUT /v2/secondopinion/patient_eligibility/{patientId}/{programName} operation.
type V2SecondopinionPatientEligibilityPatientIdProgramNamePutParams struct {
	PatientId   string
	ProgramName string
}

// V2SecondopinionPatientEligibilityProgramNamePostParams is parameters of POST /v2/secondopinion/patient_eligibility/{programName} operation.
type V2SecondopinionPatientEligibilityProgramNamePostParams struct {
	// Program name of the patient eligibility to be created.
	ProgramName string
}

// VerifyPhysicianNotificationMethodParams is parameters of verify-physician-notification-method operation.
type VerifyPhysicianNotificationMethodParams struct {
	PhysicianID string
}

// Code generated by ogen, DO NOT EDIT.

package api

import (
	"bytes"
	"io"
	"mime"
	"net/http"

	"github.com/go-faster/errors"
	"github.com/go-faster/jx"

	"github.com/ogen-go/ogen/ogenerrors"
	"github.com/ogen-go/ogen/validate"
)

func decodeAuthenticateUploadRequestV1Response(resp *http.Response) (res AuthenticateUploadRequestV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response AuthenticateUploadRequestV1OK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ErrorResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeCreateUploadRequestV1Response(resp *http.Response) (res CreateUploadRequestV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response CreateUploadRequestV1OK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ErrorResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeclineUploadRequestV1Response(resp *http.Response) (res DeclineUploadRequestV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeclineUploadRequestV1OK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeclineUploadRequestV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 409:
		// Code 409.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeclineUploadRequestV1Conflict
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeleteInternalPatientProfilesResponse(resp *http.Response) (res DeleteInternalPatientProfilesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeleteInternalPatientProfilesOK{}, nil
	case 401:
		// Code 401.
		return &DeleteInternalPatientProfilesUnauthorized{}, nil
	case 500:
		// Code 500.
		return &DeleteInternalPatientProfilesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeletePatientProfileResponse(resp *http.Response) (res DeletePatientProfileRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeletePatientProfileOK{}, nil
	case 400:
		// Code 400.
		return &DeletePatientProfileBadRequest{}, nil
	case 401:
		// Code 401.
		return &DeletePatientProfileUnauthorized{}, nil
	case 404:
		// Code 404.
		return &DeletePatientProfileNotFound{}, nil
	case 500:
		// Code 500.
		return &DeletePatientProfileInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeleteTransfersTransferIdResponse(resp *http.Response) (res DeleteTransfersTransferIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeleteTransfersTransferIdOK{}, nil
	case 401:
		// Code 401.
		return &DeleteTransfersTransferIdUnauthorized{}, nil
	case 404:
		// Code 404.
		return &DeleteTransfersTransferIdNotFound{}, nil
	case 500:
		// Code 500.
		return &DeleteTransfersTransferIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeleteUploadRequestStudyV1Response(resp *http.Response) (res DeleteUploadRequestStudyV1Res, _ error) {
	switch resp.StatusCode {
	case 204:
		// Code 204.
		return &DeleteUploadRequestStudyV1NoContent{}, nil
	case 400:
		// Code 400.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeleteUploadRequestStudyV1BadRequest
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeleteUploadRequestStudyV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeleteUploadRequestStudyV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 409:
		// Code 409.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DeleteUploadRequestStudyV1Conflict
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeleteUsersLogoutResponse(resp *http.Response) (res DeleteUsersLogoutRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeleteUsersLogoutOK{}, nil
	case 500:
		// Code 500.
		return &DeleteUsersLogoutInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeleteV2HealthrecordsPatientIdRecordsRecordIdResponse(resp *http.Response) (res DeleteV2HealthrecordsPatientIdRecordsRecordIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeleteV2HealthrecordsPatientIdRecordsRecordIdOK{}, nil
	case 400:
		// Code 400.
		return &DeleteV2HealthrecordsPatientIdRecordsRecordIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &DeleteV2HealthrecordsPatientIdRecordsRecordIdUnauthorized{}, nil
	case 404:
		// Code 404.
		return &DeleteV2HealthrecordsPatientIdRecordsRecordIdNotFound{}, nil
	case 500:
		// Code 500.
		return &DeleteV2HealthrecordsPatientIdRecordsRecordIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeDeleteV2TransfersTransferIdResponse(resp *http.Response) (res DeleteV2TransfersTransferIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &DeleteV2TransfersTransferIdOK{}, nil
	case 401:
		// Code 401.
		return &DeleteV2TransfersTransferIdUnauthorized{}, nil
	case 404:
		// Code 404.
		return &DeleteV2TransfersTransferIdNotFound{}, nil
	case 500:
		// Code 500.
		return &DeleteV2TransfersTransferIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetAccountStateResponse(resp *http.Response) (res GetAccountStateRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response AccountState
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetAccountStateUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetAccountStateInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetImageByIDResponse(resp *http.Response) (res GetImageByIDRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/dicom":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetImageByIDOKApplicationDicom{Data: bytes.NewReader(b)}
			return &response, nil
		case ct == "image/png":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetImageByIDOKImagePNG{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetImageByIDBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetImageByIDUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetImageByIDForbidden{}, nil
	case 404:
		// Code 404.
		return &GetImageByIDNotFound{}, nil
	case 500:
		// Code 500.
		return &GetImageByIDInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetImageMetadataByIDResponse(resp *http.Response) (res GetImageMetadataByIDRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ImageMetadata
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetImageMetadataByIDUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetImageMetadataByIDForbidden{}, nil
	case 404:
		// Code 404.
		return &GetImageMetadataByIDNotFound{}, nil
	case 500:
		// Code 500.
		return &GetImageMetadataByIDInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetOrganVisualizationByExamIDResponse(resp *http.Response) (res GetOrganVisualizationByExamIDRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetOrganVisualizationByExamIDOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetOrganVisualizationByExamIDUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetOrganVisualizationByExamIDNotFound{}, nil
	case 500:
		// Code 500.
		return &GetOrganVisualizationByExamIDInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPatientProfilesResponse(resp *http.Response) (res GetPatientProfilesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetPatientProfilesOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetPatientProfilesBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetPatientProfilesUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetPatientProfilesNotFound{}, nil
	case 500:
		// Code 500.
		return &GetPatientProfilesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPatientProfilesIDResponse(resp *http.Response) (res GetPatientProfilesIDRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Patient
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetPatientProfilesIDBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetPatientProfilesIDUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetPatientProfilesIDNotFound{}, nil
	case 500:
		// Code 500.
		return &GetPatientProfilesIDInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPhysicianAccountResponse(resp *http.Response) (res GetPhysicianAccountRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PhysicianAccount
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetPhysicianAccountBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetPhysicianAccountUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetPhysicianAccountNotFound{}, nil
	case 500:
		// Code 500.
		return &GetPhysicianAccountInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPhysicianAccountEunityTokenResponse(resp *http.Response) (res GetPhysicianAccountEunityTokenRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetPhysicianAccountEunityTokenOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetPhysicianAccountEunityTokenUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetPhysicianAccountEunityTokenInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPhysicianAccountPatientsResponse(resp *http.Response) (res GetPhysicianAccountPatientsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetPhysicianAccountPatientsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetPhysicianAccountPatientsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetPhysicianAccountPatientsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPhysicianAccountPatientsExamsResponse(resp *http.Response) (res GetPhysicianAccountPatientsExamsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetPhysicianAccountPatientsExamsOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetPhysicianAccountPatientsExamsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetPhysicianAccountPatientsExamsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetPhysicianAccountStudyStatesResponse(resp *http.Response) (res GetPhysicianAccountStudyStatesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetPhysicianAccountStudyStatesOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetPhysicianAccountStudyStatesUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetPhysicianAccountStudyStatesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProviderByIdResponse(resp *http.Response) (res GetProviderByIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Provider
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetProviderByIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetProviderByIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProviderConfigResponse(resp *http.Response) (res GetProviderConfigRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ProviderConfig
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &GetProviderConfigInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProviderDetailsV1Response(resp *http.Response) (res GetProviderDetailsV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetProviderDetailsResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ErrorResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProviderFormConfigResponse(resp *http.Response) (res GetProviderFormConfigRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response RequestFormConfig
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetProviderFormConfigNotFound{}, nil
	case 500:
		// Code 500.
		return &GetProviderFormConfigInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProvidersResponse(resp *http.Response) (res GetProvidersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetProvidersOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &GetProvidersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProvidersConsentsConsentidResponse(resp *http.Response) (res *ConsentFormData, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ConsentFormData
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProvidersConsentsConsentidTypeResponse(resp *http.Response) (res *ConsentType, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ConsentType
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetProvidersConsentsConsentidUnverifiedResponse(resp *http.Response) (res *ConsentFormData, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ConsentFormData
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetReferTokenResponse(resp *http.Response) (res GetReferTokenRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PublicReferEmailThrottle
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetReferTokenBadRequest{}, nil
	case 500:
		// Code 500.
		return &GetReferTokenInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetReportByIdResponse(resp *http.Response) (res GetReportByIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/pdf":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetReportByIdOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetReportByIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetReportByIdUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetReportByIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetReportByIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetReportsResponse(resp *http.Response) (res GetReportsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetReportsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetReportsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetReportsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetReportsReportIdInsightsQuestionsResponse(resp *http.Response) (res GetReportsReportIdInsightsQuestionsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &GetReportsReportIdInsightsQuestionsOK{}, nil
	case 401:
		// Code 401.
		return &GetReportsReportIdInsightsQuestionsUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetReportsReportIdInsightsQuestionsNotFound{}, nil
	case 500:
		// Code 500.
		return &GetReportsReportIdInsightsQuestionsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetReportsReportIdMetadataResponse(resp *http.Response) (res GetReportsReportIdMetadataRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Report
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetReportsReportIdMetadataUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetReportsReportIdMetadataNotFound{}, nil
	case 500:
		// Code 500.
		return &GetReportsReportIdMetadataInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetRequestsResponse(resp *http.Response) (res GetRequestsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetRequestsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetRequestsUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetRequestsForbidden{}, nil
	case 404:
		// Code 404.
		return &GetRequestsNotFound{}, nil
	case 500:
		// Code 500.
		return &GetRequestsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetShareExamsByshareIdV2Response(resp *http.Response) (res GetShareExamsByshareIdV2Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetShareExamsByshareIdV2OK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetShareExamsByshareIdV2Unauthorized{}, nil
	case 500:
		// Code 500.
		return &GetShareExamsByshareIdV2InternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetSharesResponse(resp *http.Response) (res GetSharesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Share
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetSharesUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetSharesForbidden{}, nil
	case 500:
		// Code 500.
		return &GetSharesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetSharesByIdResponse(resp *http.Response) (res GetSharesByIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Share
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		case ct == "application/zip":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetSharesByIdOKApplicationZip{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetSharesByIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetSharesByIdUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetSharesByIdForbidden{}, nil
	case 404:
		// Code 404.
		return &GetSharesByIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetSharesByIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetSharesHealthrecordsByIdResponse(resp *http.Response) (res GetSharesHealthrecordsByIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/zip":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetSharesHealthrecordsByIdOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetSharesHealthrecordsByIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetSharesHealthrecordsByIdUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetSharesHealthrecordsByIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetShortUrlsResponse(resp *http.Response) (res GetShortUrlsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ShortUrl
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetShortUrlsBadRequest{}, nil
	case 404:
		// Code 404.
		return &GetShortUrlsNotFound{}, nil
	case 500:
		// Code 500.
		return &GetShortUrlsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetTransfersTransferIdResponse(resp *http.Response) (res GetTransfersTransferIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Transfer
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetTransfersTransferIdUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetTransfersTransferIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetTransfersTransferIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetTransfersTransferIdChallengeResponse(resp *http.Response) (res GetTransfersTransferIdChallengeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ChallengeQuestion
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetTransfersTransferIdChallengeNotFound{}, nil
	case 500:
		// Code 500.
		return &GetTransfersTransferIdChallengeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUploadRequestStudyByIDV1Response(resp *http.Response) (res GetUploadRequestStudyByIDV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestStudyResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadRequestStudyByIDV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadRequestStudyByIDV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUploadRequestV1Response(resp *http.Response) (res GetUploadRequestV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadRequestResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadRequestV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadRequestV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUserExamByIdResponse(resp *http.Response) (res GetUserExamByIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Exam
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUserExamByIdUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetUserExamByIdForbidden{}, nil
	case 404:
		// Code 404.
		return &GetUserExamByIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetUserExamByIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUserExamsResponse(resp *http.Response) (res GetUserExamsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUserExamsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUserExamsUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetUserExamsForbidden{}, nil
	case 404:
		// Code 404.
		return &GetUserExamsNotFound{}, nil
	case 500:
		// Code 500.
		return &GetUserExamsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUserNotificationsResponse(resp *http.Response) (res GetUserNotificationsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Notifications
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUserNotificationsUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetUserNotificationsForbidden{}, nil
	case 404:
		// Code 404.
		return &GetUserNotificationsNotFound{}, nil
	case 500:
		// Code 500.
		return &GetUserNotificationsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUserRequestConsentResponse(resp *http.Response) (res GetUserRequestConsentRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/pdf":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetUserRequestConsentOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUserRequestConsentUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetUserRequestConsentForbidden{}, nil
	case 404:
		// Code 404.
		return &GetUserRequestConsentNotFound{}, nil
	case 500:
		// Code 500.
		return &GetUserRequestConsentInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUsersExamsLookupResponse(resp *http.Response) (res GetUsersExamsLookupRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUsersExamsLookupOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetUsersExamsLookupBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetUsersExamsLookupUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetUsersExamsLookupNotFound{}, nil
	case 500:
		// Code 500.
		return &GetUsersExamsLookupInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUsersExamsSizeResponse(resp *http.Response) (res GetUsersExamsSizeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUsersExamsSizeOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUsersExamsSizeUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetUsersExamsSizeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUsersRolloutFlagResponse(resp *http.Response) (res GetUsersRolloutFlagRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUsersRolloutFlagOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &GetUsersRolloutFlagInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetUsersSettingsResponse(resp *http.Response) (res GetUsersSettingsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUsersSettingsOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetUsersSettingsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetUsersSettingsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1AppointmentsResponse(resp *http.Response) (res GetV1AppointmentsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response AppointmentDetails
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1AppointmentsBadRequest
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1AppointmentsUnauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1AppointmentsInternalServerError
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1ExpertreviewPaymentprovidersResponse(resp *http.Response) (res GetV1ExpertreviewPaymentprovidersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1ExpertreviewPaymentprovidersOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV1ExpertreviewPaymentprovidersUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV1ExpertreviewPaymentprovidersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1FeaturesIDAuthorizeResponse(resp *http.Response) (res *GetV1FeaturesIDAuthorizeOK, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &GetV1FeaturesIDAuthorizeOK{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1PlansResponse(resp *http.Response) (res *GetV1PlansOK, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &GetV1PlansOK{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1PromoCodeResponse(resp *http.Response) (res GetV1PromoCodeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1PromoCodeOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetV1PromoCodeNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1PromoCodeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1ReportsIDTaggedhtmlResponse(resp *http.Response) (res GetV1ReportsIDTaggedhtmlRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response TaggedHTML
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV1ReportsIDTaggedhtmlUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetV1ReportsIDTaggedhtmlNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1ReportsIDTaggedhtmlInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1ReportsReportIdInsightsFollowupResponse(resp *http.Response) (res GetV1ReportsReportIdInsightsFollowupRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &GetV1ReportsReportIdInsightsFollowupOK{}, nil
	case 401:
		// Code 401.
		return &GetV1ReportsReportIdInsightsFollowupUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetV1ReportsReportIdInsightsFollowupNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1ReportsReportIdInsightsFollowupInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1RequestsIncompleteIncompleteRequestIdResponse(resp *http.Response) (res GetV1RequestsIncompleteIncompleteRequestIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response IncompleteRequestMetadata
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetV1RequestsIncompleteIncompleteRequestIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1RequestsIncompleteIncompleteRequestIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1RequestsIncompleteIncompleteRequestIdEmailResponse(resp *http.Response) (res GetV1RequestsIncompleteIncompleteRequestIdEmailRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1RequestsIncompleteIncompleteRequestIdEmailOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetV1RequestsIncompleteIncompleteRequestIdEmailNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV1RequestsIncompleteIncompleteRequestIdEmailInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1RequestsVerificationConfigurationResponse(resp *http.Response) (res GetV1RequestsVerificationConfigurationRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV1RequestsVerificationConfigurationOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &GetV1RequestsVerificationConfigurationInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV1SecondopinionEligibleExamIdResponse(resp *http.Response) (res *SOEligibility, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response SOEligibility
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2HealthrecordsMychartSearchResponse(resp *http.Response) (res GetV2HealthrecordsMychartSearchRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2HealthrecordsMychartSearchOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV2HealthrecordsMychartSearchBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV2HealthrecordsMychartSearchUnauthorized{}, nil
	case 403:
		// Code 403.
		return &GetV2HealthrecordsMychartSearchForbidden{}, nil
	case 500:
		// Code 500.
		return &GetV2HealthrecordsMychartSearchInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2HealthrecordsPatientIdRecordsRecordIdResponse(resp *http.Response) (res GetV2HealthrecordsPatientIdRecordsRecordIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/zip":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := GetV2HealthrecordsPatientIdRecordsRecordIdOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV2HealthrecordsPatientIdRecordsRecordIdUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2HealthrecordsPatientIdRecordsRecordIdThumbnailResponse(resp *http.Response) (res GetV2HealthrecordsPatientIdRecordsRecordIdThumbnailRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &GetV2HealthrecordsPatientIdRecordsRecordIdThumbnailOK{}, nil
	case 401:
		// Code 401.
		return &GetV2HealthrecordsPatientIdRecordsRecordIdThumbnailUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2HealthrecordsPatientIdRecordsRecordTypeResponse(resp *http.Response) (res GetV2HealthrecordsPatientIdRecordsRecordTypeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2HealthrecordsPatientIdRecordsRecordTypeOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV2HealthrecordsPatientIdRecordsRecordTypeUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2HealthrecordsPatientIdUploadResponse(resp *http.Response) (res GetV2HealthrecordsPatientIdUploadRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2HealthrecordsPatientIdUploadOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV2HealthrecordsPatientIdUploadUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2OrdersResponse(resp *http.Response) (res GetV2OrdersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2OrdersOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV2OrdersUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV2OrdersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2PatientIdResponse(resp *http.Response) (res GetV2PatientIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2PatientIdOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		return &GetV2PatientIdNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV2PatientIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2ProvidersResponse(resp *http.Response) (res GetV2ProvidersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2ProvidersOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &GetV2ProvidersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2RhoEligibleExamIdResponse(resp *http.Response) (res GetV2RhoEligibleExamIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response AnalysisEligibility
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV2RhoEligibleExamIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV2RhoEligibleExamIdUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetV2RhoEligibleExamIdNotFound{}, nil
	case 422:
		// Code 422.
		return &GetV2RhoEligibleExamIdUnprocessableEntity{}, nil
	case 500:
		// Code 500.
		return &GetV2RhoEligibleExamIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2SecondopiniontDoctorsSearchResponse(resp *http.Response) (res GetV2SecondopiniontDoctorsSearchRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2SecondopiniontDoctorsSearchOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &GetV2SecondopiniontDoctorsSearchUnauthorized{}, nil
	case 500:
		// Code 500.
		return &GetV2SecondopiniontDoctorsSearchInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeGetV2UsersExamsLookupResponse(resp *http.Response) (res GetV2UsersExamsLookupRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetV2UsersExamsLookupOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &GetV2UsersExamsLookupBadRequest{}, nil
	case 401:
		// Code 401.
		return &GetV2UsersExamsLookupUnauthorized{}, nil
	case 404:
		// Code 404.
		return &GetV2UsersExamsLookupNotFound{}, nil
	case 500:
		// Code 500.
		return &GetV2UsersExamsLookupInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeListUploadRequestStudiesV1Response(resp *http.Response) (res ListUploadRequestStudiesV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestStudiesResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ListUploadRequestStudiesV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ListUploadRequestStudiesV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchPatientProfileResponse(resp *http.Response) (res PatchPatientProfileRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchPatientProfileOK{}, nil
	case 400:
		// Code 400.
		return &PatchPatientProfileBadRequest{}, nil
	case 401:
		// Code 401.
		return &PatchPatientProfileUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchPatientProfileNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchPatientProfileInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchPhysicianAccountsResponse(resp *http.Response) (res PatchPhysicianAccountsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchPhysicianAccountsOK{}, nil
	case 400:
		// Code 400.
		return &PatchPhysicianAccountsBadRequest{}, nil
	case 401:
		// Code 401.
		return &PatchPhysicianAccountsUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchPhysicianAccountsNotFound{}, nil
	case 406:
		// Code 406.
		return &PatchPhysicianAccountsNotAcceptable{}, nil
	case 500:
		// Code 500.
		return &PatchPhysicianAccountsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchRequestsIDCancelResponse(resp *http.Response) (res PatchRequestsIDCancelRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchRequestsIDCancelOK{}, nil
	case 400:
		// Code 400.
		return &PatchRequestsIDCancelBadRequest{}, nil
	case 401:
		// Code 401.
		return &PatchRequestsIDCancelUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchRequestsIDCancelNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchRequestsIDCancelInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchUsersDeactivateAccountResponse(resp *http.Response) (res PatchUsersDeactivateAccountRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchUsersDeactivateAccountOK{}, nil
	case 400:
		// Code 400.
		return &PatchUsersDeactivateAccountBadRequest{}, nil
	case 401:
		// Code 401.
		return &PatchUsersDeactivateAccountUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchUsersDeactivateAccountNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchUsersDeactivateAccountInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchUsersSetAccountOwnerResponse(resp *http.Response) (res PatchUsersSetAccountOwnerRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchUsersSetAccountOwnerOK{}, nil
	case 400:
		// Code 400.
		return &PatchUsersSetAccountOwnerBadRequest{}, nil
	case 401:
		// Code 401.
		return &PatchUsersSetAccountOwnerUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PatchUsersSetAccountOwnerNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchUsersSetAccountOwnerInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePatchV1RequestsIncompleteIncompleteRequestIdResponse(resp *http.Response) (res PatchV1RequestsIncompleteIncompleteRequestIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PatchV1RequestsIncompleteIncompleteRequestIdOK{}, nil
	case 400:
		// Code 400.
		return &PatchV1RequestsIncompleteIncompleteRequestIdBadRequest{}, nil
	case 404:
		// Code 404.
		return &PatchV1RequestsIncompleteIncompleteRequestIdNotFound{}, nil
	case 500:
		// Code 500.
		return &PatchV1RequestsIncompleteIncompleteRequestIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostEmailVerificationResponse(resp *http.Response) (res PostEmailVerificationRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response EmailVerification
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostEmailVerificationBadRequest{}, nil
	case 500:
		// Code 500.
		return &PostEmailVerificationInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPatientProfilesResponse(resp *http.Response) (res PostPatientProfilesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostPatientProfilesOK{}, nil
	case 400:
		// Code 400.
		return &PostPatientProfilesBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPatientProfilesUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PostPatientProfilesForbidden{}, nil
	case 404:
		// Code 404.
		return &PostPatientProfilesNotFound{}, nil
	case 500:
		// Code 500.
		return &PostPatientProfilesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountStudyAccessResponse(resp *http.Response) (res PostPhysicianAccountStudyAccessRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostPhysicianAccountStudyAccessOK{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountStudyAccessUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountStudyAccessInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsResponse(resp *http.Response) (res PostPhysicianAccountsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostPhysicianAccountsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostPhysicianAccountsBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountsUnauthorized{}, nil
	case 406:
		// Code 406.
		return &PostPhysicianAccountsNotAcceptable{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsLoginResponse(resp *http.Response) (res PostPhysicianAccountsLoginRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostPhysicianAccountsLoginOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostPhysicianAccountsLoginBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountsLoginUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PostPhysicianAccountsLoginForbidden{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsLoginInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsLogoutResponse(resp *http.Response) (res PostPhysicianAccountsLogoutRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostPhysicianAccountsLogoutOK{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsLogoutInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsRequestResponse(resp *http.Response) (res PostPhysicianAccountsRequestRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostPhysicianAccountsRequestOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostPhysicianAccountsRequestBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountsRequestUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsRequestInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsResetResponse(resp *http.Response) (res PostPhysicianAccountsResetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostPhysicianAccountsResetOK{}, nil
	case 400:
		// Code 400.
		return &PostPhysicianAccountsResetBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountsResetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostPhysicianAccountsResetNotFound{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsResetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsSearchResponse(resp *http.Response) (res PostPhysicianAccountsSearchRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "text/plain":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := PostPhysicianAccountsSearchOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostPhysicianAccountsSearchBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountsSearchUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostPhysicianAccountsSearchNotFound{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsSearchInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianAccountsVerifyResponse(resp *http.Response) (res PostPhysicianAccountsVerifyRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostPhysicianAccountsVerifyOK{}, nil
	case 400:
		// Code 400.
		return &PostPhysicianAccountsVerifyBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianAccountsVerifyUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostPhysicianAccountsVerifyNotFound{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianAccountsVerifyInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostPhysicianLicenseResponse(resp *http.Response) (res PostPhysicianLicenseRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostPhysicianLicenseOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostPhysicianLicenseBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostPhysicianLicenseUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostPhysicianLicenseNotFound{}, nil
	case 500:
		// Code 500.
		return &PostPhysicianLicenseInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostProvidersConsentsConsentidResponse(resp *http.Response) (res PostProvidersConsentsConsentidRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/pdf":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := PostProvidersConsentsConsentidOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostProvidersConsentsConsentidBadRequest{}, nil
	case 404:
		// Code 404.
		return &PostProvidersConsentsConsentidNotFound{}, nil
	case 500:
		// Code 500.
		return &PostProvidersConsentsConsentidInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostProvidersConsentsConsentidUnverifiedResponse(resp *http.Response) (res PostProvidersConsentsConsentidUnverifiedRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/pdf":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := PostProvidersConsentsConsentidUnverifiedOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostProvidersConsentsConsentidUnverifiedBadRequest{}, nil
	case 404:
		// Code 404.
		return &PostProvidersConsentsConsentidUnverifiedNotFound{}, nil
	case 500:
		// Code 500.
		return &PostProvidersConsentsConsentidUnverifiedInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostProvidersConsentsEmailVerificationResponse(resp *http.Response) (res PostProvidersConsentsEmailVerificationRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ConsentEmailVerification
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostProvidersConsentsEmailVerificationBadRequest{}, nil
	case 404:
		// Code 404.
		return &PostProvidersConsentsEmailVerificationNotFound{}, nil
	case 500:
		// Code 500.
		return &PostProvidersConsentsEmailVerificationInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostReferTokenResponse(resp *http.Response) (res PostReferTokenRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostReferTokenOK{}, nil
	case 400:
		// Code 400.
		return &PostReferTokenBadRequest{}, nil
	case 500:
		// Code 500.
		return &PostReferTokenInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostReportViewsResponse(resp *http.Response) (res PostReportViewsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostReportViewsOK{}, nil
	case 401:
		// Code 401.
		return &PostReportViewsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostReportViewsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostRequestsResponse(resp *http.Response) (res PostRequestsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostRequestsOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostRequestsBadRequest{}, nil
	case 402:
		// Code 402.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PaymentError
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &PostRequestsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostRequestsIDRejectVerifyResponse(resp *http.Response) (res *PostRequestsIDRejectVerifyOK, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostRequestsIDRejectVerifyOK{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostRequestsIDResubmitResponse(resp *http.Response) (res PostRequestsIDResubmitRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostRequestsIDResubmitOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostRequestsIDResubmitBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostRequestsIDResubmitUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostRequestsIDResubmitNotFound{}, nil
	case 500:
		// Code 500.
		return &PostRequestsIDResubmitInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostRequestsUphResponse(resp *http.Response) (res PostRequestsUphRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostRequestsUphOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostRequestsUphBadRequest{}, nil
	case 402:
		// Code 402.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PaymentError
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &PostRequestsUphInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostReshareResponse(resp *http.Response) (res PostReshareRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/pdf":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := PostReshareOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &PostReshareUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PostReshareForbidden{}, nil
	case 404:
		// Code 404.
		return &PostReshareNotFound{}, nil
	case 500:
		// Code 500.
		return &PostReshareInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostSharesResponse(resp *http.Response) (res PostSharesRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostSharesOK{}, nil
	case 400:
		// Code 400.
		return &PostSharesBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostSharesUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PostSharesForbidden{}, nil
	case 500:
		// Code 500.
		return &PostSharesInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostSharesValidateResponse(resp *http.Response) (res PostSharesValidateRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostSharesValidateOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostSharesValidateBadRequest{}, nil
	case 500:
		// Code 500.
		return &PostSharesValidateInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostShortUrlsResponse(resp *http.Response) (res PostShortUrlsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ShortUrl
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostShortUrlsBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostShortUrlsUnauthorized{}, nil
	case 422:
		// Code 422.
		return &PostShortUrlsUnprocessableEntity{}, nil
	case 500:
		// Code 500.
		return &PostShortUrlsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostTransferPaymentResponse(resp *http.Response) (res PostTransferPaymentRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostTransferPaymentOK{}, nil
	case 400:
		// Code 400.
		return &PostTransferPaymentBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostTransferPaymentUnauthorized{}, nil
	case 402:
		// Code 402.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PaymentError
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 403:
		// Code 403.
		return &PostTransferPaymentForbidden{}, nil
	case 404:
		// Code 404.
		return &PostTransferPaymentNotFound{}, nil
	case 500:
		// Code 500.
		return &PostTransferPaymentInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostTransfersTransferIdChallengeResponse(resp *http.Response) (res PostTransfersTransferIdChallengeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response ChallengeToken
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostTransfersTransferIdChallengeBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostTransfersTransferIdChallengeUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PostTransfersTransferIdChallengeForbidden{}, nil
	case 404:
		// Code 404.
		return &PostTransfersTransferIdChallengeNotFound{}, nil
	case 500:
		// Code 500.
		return &PostTransfersTransferIdChallengeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostTransfersTransferIdReactivateResponse(resp *http.Response) (res PostTransfersTransferIdReactivateRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostTransfersTransferIdReactivateOK{}, nil
	case 500:
		// Code 500.
		return &PostTransfersTransferIdReactivateInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUserNotificationResponse(resp *http.Response) (res PostUserNotificationRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostUserNotificationOK{}, nil
	case 201:
		// Code 201.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response Notification
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostUserNotificationBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostUserNotificationUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostUserNotificationInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUsersLockAccountResponse(resp *http.Response) (res PostUsersLockAccountRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostUsersLockAccountOK{}, nil
	case 400:
		// Code 400.
		return &PostUsersLockAccountBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostUsersLockAccountUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostUsersLockAccountNotFound{}, nil
	case 500:
		// Code 500.
		return &PostUsersLockAccountInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUsersLoginSSOResponse(resp *http.Response) (res PostUsersLoginSSORes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostUsersLoginSSOOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostUsersLoginSSOBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostUsersLoginSSOUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PostUsersLoginSSOForbidden{}, nil
	case 500:
		// Code 500.
		return &PostUsersLoginSSOInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUsersReferralResponse(resp *http.Response) (res PostUsersReferralRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostUsersReferralOK{}, nil
	case 500:
		// Code 500.
		return &PostUsersReferralInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUsersSetupResponse(resp *http.Response) (res PostUsersSetupRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostUsersSetupOK{}, nil
	case 400:
		// Code 400.
		return &PostUsersSetupBadRequest{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUsersUserIdResponse(resp *http.Response) (res PostUsersUserIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostUsersUserIdOK{}, nil
	case 400:
		// Code 400.
		return &PostUsersUserIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostUsersUserIdUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostUsersUserIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostUsersVerifyResponse(resp *http.Response) (res PostUsersVerifyRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostUsersVerifyOK{}, nil
	case 400:
		// Code 400.
		return &PostUsersVerifyBadRequest{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1AppointmentsPatientstatusResponse(resp *http.Response) (res PostV1AppointmentsPatientstatusRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV1AppointmentsPatientstatusOK{}, nil
	case 400:
		// Code 400.
		return &PostV1AppointmentsPatientstatusBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV1AppointmentsPatientstatusUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostV1AppointmentsPatientstatusNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV1AppointmentsPatientstatusInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1RequestsIncompleteResponse(resp *http.Response) (res PostV1RequestsIncompleteRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response IncompleteRequestInitResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV1RequestsIncompleteBadRequest{}, nil
	case 500:
		// Code 500.
		return &PostV1RequestsIncompleteInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1RequestsIncompleteIncompleteRequestIdEmailStatusResponse(resp *http.Response) (res PostV1RequestsIncompleteIncompleteRequestIdEmailStatusRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV1RequestsIncompleteIncompleteRequestIdEmailStatusOK{}, nil
	case 400:
		// Code 400.
		return &PostV1RequestsIncompleteIncompleteRequestIdEmailStatusBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV1RequestsIncompleteIncompleteRequestIdEmailStatusUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostV1RequestsIncompleteIncompleteRequestIdEmailStatusInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1RequestsIncompleteIncompleteRequestIdVerifyResponse(resp *http.Response) (res PostV1RequestsIncompleteIncompleteRequestIdVerifyRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response IncompleteRequestResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV1RequestsIncompleteIncompleteRequestIdVerifyBadRequest{}, nil
	case 404:
		// Code 404.
		return &PostV1RequestsIncompleteIncompleteRequestIdVerifyNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV1RequestsIncompleteIncompleteRequestIdVerifyInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1SecondopinionReviewsResponse(resp *http.Response) (res PostV1SecondopinionReviewsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV1SecondopinionReviewsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV1SecondopinionReviewsBadRequest
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV1SecondopinionReviewsUnauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &PostV1SecondopinionReviewsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV1SharesShareIdDltokenResponse(resp *http.Response) (res PostV1SharesShareIdDltokenRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response DLToken
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &PostV1SharesShareIdDltokenUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostV1SharesShareIdDltokenNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV1SharesShareIdDltokenInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2HealthrecordsMychartPatientIdResponse(resp *http.Response) (res PostV2HealthrecordsMychartPatientIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2HealthrecordsMychartPatientIdOK{}, nil
	case 400:
		// Code 400.
		return &PostV2HealthrecordsMychartPatientIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2HealthrecordsMychartPatientIdUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostV2HealthrecordsMychartPatientIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2HealthrecordsPatientIdResponse(resp *http.Response) (res PostV2HealthrecordsPatientIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2HealthrecordsPatientIdOK{}, nil
	case 400:
		// Code 400.
		return &PostV2HealthrecordsPatientIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2HealthrecordsPatientIdUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostV2HealthrecordsPatientIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2HealthrecordsPatientIdGailResponse(resp *http.Response) (res PostV2HealthrecordsPatientIdGailRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GailRiskScore
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV2HealthrecordsPatientIdGailBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2HealthrecordsPatientIdGailUnauthorized{}, nil
	case 422:
		// Code 422.
		return &PostV2HealthrecordsPatientIdGailUnprocessableEntity{}, nil
	case 500:
		// Code 500.
		return &PostV2HealthrecordsPatientIdGailInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2HealthrecordsVerifyResponse(resp *http.Response) (res PostV2HealthrecordsVerifyRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV2HealthrecordsVerifyOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &PostV2HealthrecordsVerifyUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2OrdersResponse(resp *http.Response) (res PostV2OrdersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response CreateOrderResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV2OrdersBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2OrdersUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostV2OrdersNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV2OrdersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2RhoRequestResponse(resp *http.Response) (res PostV2RhoRequestRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV2RhoRequestOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &PostV2RhoRequestUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostV2RhoRequestInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2SecondopinionEligiblePriorsResponse(resp *http.Response) (res PostV2SecondopinionEligiblePriorsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostV2SecondopinionEligiblePriorsOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &PostV2SecondopinionEligiblePriorsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostV2SecondopinionEligiblePriorsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2TransfersResponse(resp *http.Response) (res PostV2TransfersRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadInitResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostV2TransfersBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2TransfersUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PostV2TransfersInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2TransfersTransferIdFileResponse(resp *http.Response) (res PostV2TransfersTransferIdFileRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2TransfersTransferIdFileOK{}, nil
	case 400:
		// Code 400.
		return &PostV2TransfersTransferIdFileBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2TransfersTransferIdFileUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostV2TransfersTransferIdFileNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV2TransfersTransferIdFileInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2TransfersTransferIdFinalizeResponse(resp *http.Response) (res PostV2TransfersTransferIdFinalizeRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2TransfersTransferIdFinalizeOK{}, nil
	case 401:
		// Code 401.
		return &PostV2TransfersTransferIdFinalizeUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostV2TransfersTransferIdFinalizeNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV2TransfersTransferIdFinalizeInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2TransfersTransferIdReportdcmResponse(resp *http.Response) (res PostV2TransfersTransferIdReportdcmRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2TransfersTransferIdReportdcmOK{}, nil
	case 400:
		// Code 400.
		return &PostV2TransfersTransferIdReportdcmBadRequest{}, nil
	case 401:
		// Code 401.
		return &PostV2TransfersTransferIdReportdcmUnauthorized{}, nil
	case 404:
		// Code 404.
		return &PostV2TransfersTransferIdReportdcmNotFound{}, nil
	case 500:
		// Code 500.
		return &PostV2TransfersTransferIdReportdcmInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2UsersResetpasswordResponse(resp *http.Response) (res PostV2UsersResetpasswordRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2UsersResetpasswordOK{}, nil
	case 400:
		// Code 400.
		return &PostV2UsersResetpasswordBadRequest{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2UsersResetpasswordInitResponse(resp *http.Response) (res *PostV2UsersResetpasswordInitOK, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2UsersResetpasswordInitOK{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostV2usersUpdateEmailResponse(resp *http.Response) (res *PostV2usersUpdateEmailOK, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PostV2usersUpdateEmailOK{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePostVerifyDobResponse(resp *http.Response) (res PostVerifyDobRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PostVerifyDobOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &PostVerifyDobBadRequest{}, nil
	case 500:
		// Code 500.
		return &PostVerifyDobInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutNotificationReadByIdResponse(resp *http.Response) (res PutNotificationReadByIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response NotificationRead
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &PutNotificationReadByIdUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PutNotificationReadByIdForbidden{}, nil
	case 404:
		// Code 404.
		return &PutNotificationReadByIdNotFound{}, nil
	case 500:
		// Code 500.
		return &PutNotificationReadByIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutPhysicianAccountsShareExtendResponse(resp *http.Response) (res PutPhysicianAccountsShareExtendRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutPhysicianAccountsShareExtendOK{}, nil
	case 401:
		// Code 401.
		return &PutPhysicianAccountsShareExtendUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PutPhysicianAccountsShareExtendInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutRevokeShareResponse(resp *http.Response) (res PutRevokeShareRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutRevokeShareOK{}, nil
	case 401:
		// Code 401.
		return &PutRevokeShareUnauthorized{}, nil
	case 403:
		// Code 403.
		return &PutRevokeShareForbidden{}, nil
	case 404:
		// Code 404.
		return &PutRevokeShareNotFound{}, nil
	case 500:
		// Code 500.
		return &PutRevokeShareInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutShareExtendByshareIdResponse(resp *http.Response) (res PutShareExtendByshareIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutShareExtendByshareIdOK{}, nil
	case 401:
		// Code 401.
		return &PutShareExtendByshareIdUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PutShareExtendByshareIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutUsersSubscriptionToggleautorenewResponse(resp *http.Response) (res PutUsersSubscriptionToggleautorenewRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutUsersSubscriptionToggleautorenewOK{}, nil
	case 401:
		// Code 401.
		return &PutUsersSubscriptionToggleautorenewUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PutUsersSubscriptionToggleautorenewInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutUsersUpdateEmailResponse(resp *http.Response) (res PutUsersUpdateEmailRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutUsersUpdateEmailOK{}, nil
	case 400:
		// Code 400.
		return &PutUsersUpdateEmailBadRequest{}, nil
	case 500:
		// Code 500.
		return &PutUsersUpdateEmailInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutV2HealthrecordsPatientIdRecordsRecordIdResponse(resp *http.Response) (res PutV2HealthrecordsPatientIdRecordsRecordIdRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutV2HealthrecordsPatientIdRecordsRecordIdOK{}, nil
	case 400:
		// Code 400.
		return &PutV2HealthrecordsPatientIdRecordsRecordIdBadRequest{}, nil
	case 401:
		// Code 401.
		return &PutV2HealthrecordsPatientIdRecordsRecordIdUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PutV2HealthrecordsPatientIdRecordsRecordIdInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodePutV2OrdersPaymentDetailsResponse(resp *http.Response) (res PutV2OrdersPaymentDetailsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &PutV2OrdersPaymentDetailsOK{}, nil
	case 400:
		// Code 400.
		return &PutV2OrdersPaymentDetailsBadRequest{}, nil
	case 401:
		// Code 401.
		return &PutV2OrdersPaymentDetailsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &PutV2OrdersPaymentDetailsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeSubmitUploadRequestV1Response(resp *http.Response) (res SubmitUploadRequestV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response SubmitUploadRequestV1OK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response SubmitUploadRequestV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response SubmitUploadRequestV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 409:
		// Code 409.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response SubmitUploadRequestV1Conflict
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeUpdateUploadRequestStudyV1Response(resp *http.Response) (res UpdateUploadRequestStudyV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestStudyResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestStudyV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestStudyV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 409:
		// Code 409.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestStudyV1Conflict
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeUpdateUploadRequestV1Response(resp *http.Response) (res UpdateUploadRequestV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response GetUploadRequestResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestV1BadRequest
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 403:
		// Code 403.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestV1Forbidden
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 409:
		// Code 409.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UpdateUploadRequestV1Conflict
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeUpdateUserSettingsResponse(resp *http.Response) (res UpdateUserSettingsRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &UpdateUserSettingsOK{}, nil
	case 401:
		// Code 401.
		return &UpdateUserSettingsUnauthorized{}, nil
	case 500:
		// Code 500.
		return &UpdateUserSettingsInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeUploadRequestUploadInstanceV1Response(resp *http.Response) (res UploadRequestUploadInstanceV1Res, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestUploadInstanceV1OK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestUploadInstanceV1BadRequest
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestUploadInstanceV1Unauthorized
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 404:
		// Code 404.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestUploadInstanceV1NotFound
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 409:
		// Code 409.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response UploadRequestUploadInstanceV1Conflict
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1MeddreamGenerateExamUUIDGetResponse(resp *http.Response) (res V1MeddreamGenerateExamUUIDGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1MeddreamGenerateExamUUIDGetOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1MeddreamGenerateExamUUIDGetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1MeddreamGenerateExamUUIDGetNotFound{}, nil
	case 500:
		// Code 500.
		return &V1MeddreamGenerateExamUUIDGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1RecordsUploadStatusGetResponse(resp *http.Response) (res V1RecordsUploadStatusGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1RecordsUploadStatusGetOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1RecordsUploadStatusGetUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V1RecordsUploadStatusGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1UsersExamsEligibleInsightsGetResponse(resp *http.Response) (res V1UsersExamsEligibleInsightsGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V1UsersExamsEligibleInsightsGetOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V1UsersExamsEligibleInsightsGetBadRequest{}, nil
	case 401:
		// Code 401.
		return &V1UsersExamsEligibleInsightsGetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1UsersExamsEligibleInsightsGetNotFound{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1UsersExamsExamuuidThumbnailGetResponse(resp *http.Response) (res V1UsersExamsExamuuidThumbnailGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/png":
			reader := resp.Body
			b, err := io.ReadAll(reader)
			if err != nil {
				return res, err
			}

			response := V1UsersExamsExamuuidThumbnailGetOK{Data: bytes.NewReader(b)}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1UsersExamsExamuuidThumbnailGetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1UsersExamsExamuuidThumbnailGetNotFound{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1UsersNotificationsNotificationIdDeleteResponse(resp *http.Response) (res V1UsersNotificationsNotificationIdDeleteRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &V1UsersNotificationsNotificationIdDeleteOK{}, nil
	case 401:
		// Code 401.
		return &V1UsersNotificationsNotificationIdDeleteUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1UsersNotificationsNotificationIdDeleteNotFound{}, nil
	case 500:
		// Code 500.
		return &V1UsersNotificationsNotificationIdDeleteInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV1UsersReportinsightsGetResponse(resp *http.Response) (res V1UsersReportinsightsGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response InsightsResponse
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 401:
		// Code 401.
		return &V1UsersReportinsightsGetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V1UsersReportinsightsGetNotFound{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2PatientsIncompleteNotificationPostResponse(resp *http.Response) (res V2PatientsIncompleteNotificationPostRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &V2PatientsIncompleteNotificationPostOK{}, nil
	case 201:
		// Code 201.
		return &V2PatientsIncompleteNotificationPostCreated{}, nil
	case 401:
		// Code 401.
		return &V2PatientsIncompleteNotificationPostUnauthorized{}, nil
	case 500:
		// Code 500.
		return &V2PatientsIncompleteNotificationPostInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2PatientsPatientIDValidGetResponse(resp *http.Response) (res V2PatientsPatientIDValidGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PatientProfileValidation
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V2PatientsPatientIDValidGetBadRequest{}, nil
	case 401:
		// Code 401.
		return &V2PatientsPatientIDValidGetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V2PatientsPatientIDValidGetNotFound{}, nil
	case 500:
		// Code 500.
		return &V2PatientsPatientIDValidGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2PatientsValidGetResponse(resp *http.Response) (res V2PatientsValidGetRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V2PatientsValidGetOK
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V2PatientsValidGetBadRequest{}, nil
	case 401:
		// Code 401.
		return &V2PatientsValidGetUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V2PatientsValidGetNotFound{}, nil
	case 500:
		// Code 500.
		return &V2PatientsValidGetInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2RequestsCreatePostResponse(resp *http.Response) (res V2RequestsCreatePostRes, _ error) {
	switch resp.StatusCode {
	case 201:
		// Code 201.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V2RequestsCreatePostCreated
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V2RequestsCreatePostBadRequest{}, nil
	case 401:
		// Code 401.
		return &V2RequestsCreatePostUnauthorized{}, nil
	case 402:
		// Code 402.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response PaymentError
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 500:
		// Code 500.
		return &V2RequestsCreatePostInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2RequestsRidStatusHistoryPostResponse(resp *http.Response) (res V2RequestsRidStatusHistoryPostRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response V2RequestsRidStatusHistoryPostOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			// Validate response.
			if err := func() error {
				if err := response.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return res, errors.Wrap(err, "validate")
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &V2RequestsRidStatusHistoryPostBadRequest{}, nil
	case 401:
		// Code 401.
		return &V2RequestsRidStatusHistoryPostUnauthorized{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2SecondopinionPatientEligibilityPatientIdProgramNamePutResponse(resp *http.Response) (res V2SecondopinionPatientEligibilityPatientIdProgramNamePutRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &V2SecondopinionPatientEligibilityPatientIdProgramNamePutOK{}, nil
	case 400:
		// Code 400.
		return &V2SecondopinionPatientEligibilityPatientIdProgramNamePutBadRequest{}, nil
	case 401:
		// Code 401.
		return &V2SecondopinionPatientEligibilityPatientIdProgramNamePutUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V2SecondopinionPatientEligibilityPatientIdProgramNamePutNotFound{}, nil
	case 500:
		// Code 500.
		return &V2SecondopinionPatientEligibilityPatientIdProgramNamePutInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2SecondopinionPatientEligibilityPostResponse(resp *http.Response) (res V2SecondopinionPatientEligibilityPostRes, _ error) {
	switch resp.StatusCode {
	case 201:
		// Code 201.
		return &V2SecondopinionPatientEligibilityPostCreated{}, nil
	case 400:
		// Code 400.
		return &V2SecondopinionPatientEligibilityPostBadRequest{}, nil
	case 401:
		// Code 401.
		return &V2SecondopinionPatientEligibilityPostUnauthorized{}, nil
	case 404:
		// Code 404.
		return &V2SecondopinionPatientEligibilityPostNotFound{}, nil
	case 500:
		// Code 500.
		return &V2SecondopinionPatientEligibilityPostInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeV2SecondopinionPatientEligibilityProgramNamePostResponse(resp *http.Response) (res V2SecondopinionPatientEligibilityProgramNamePostRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		return &V2SecondopinionPatientEligibilityProgramNamePostOK{}, nil
	case 201:
		// Code 201.
		return &V2SecondopinionPatientEligibilityProgramNamePostCreated{}, nil
	case 400:
		// Code 400.
		return &V2SecondopinionPatientEligibilityProgramNamePostBadRequest{}, nil
	case 500:
		// Code 500.
		return &V2SecondopinionPatientEligibilityProgramNamePostInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

func decodeVerifyPhysicianNotificationMethodResponse(resp *http.Response) (res VerifyPhysicianNotificationMethodRes, _ error) {
	switch resp.StatusCode {
	case 200:
		// Code 200.
		ct, _, err := mime.ParseMediaType(resp.Header.Get("Content-Type"))
		if err != nil {
			return res, errors.Wrap(err, "parse media type")
		}
		switch {
		case ct == "application/json":
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				return res, err
			}
			d := jx.DecodeBytes(buf)

			var response VerifyPhysicianNotificationMethodOKApplicationJSON
			if err := func() error {
				if err := response.Decode(d); err != nil {
					return err
				}
				if err := d.Skip(); err != io.EOF {
					return errors.New("unexpected trailing data")
				}
				return nil
			}(); err != nil {
				err = &ogenerrors.DecodeBodyError{
					ContentType: ct,
					Body:        buf,
					Err:         err,
				}
				return res, err
			}
			return &response, nil
		default:
			return res, validate.InvalidContentType(ct)
		}
	case 400:
		// Code 400.
		return &VerifyPhysicianNotificationMethodBadRequest{}, nil
	case 401:
		// Code 401.
		return &VerifyPhysicianNotificationMethodUnauthorized{}, nil
	case 404:
		// Code 404.
		return &VerifyPhysicianNotificationMethodNotFound{}, nil
	case 500:
		// Code 500.
		return &VerifyPhysicianNotificationMethodInternalServerError{}, nil
	}
	return res, validate.UnexpectedStatusCode(resp.StatusCode)
}

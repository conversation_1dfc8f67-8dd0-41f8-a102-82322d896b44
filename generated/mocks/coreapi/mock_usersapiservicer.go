// Code generated by mockery v2.52.2. DO NOT EDIT.

package mockcoreapi

import (
	context "context"

	accountservice "gitlab.com/pockethealth/coreapi/pkg/services/accountservice"

	coreapi "gitlab.com/pockethealth/coreapi/pkg/coreapi"

	io "io"

	mock "github.com/stretchr/testify/mock"

	queries "gitlab.com/pockethealth/phutils/v10/pkg/queries"

	reportinsights "gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
)

// MockUsersApiServicer is an autogenerated mock type for the UsersApiServicer type
type MockUsersApiServicer struct {
	mock.Mock
}

type MockUsersApiServicer_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUsersApiServicer) EXPECT() *MockUsersApiServicer_Expecter {
	return &MockUsersApiServicer_Expecter{mock: &_m.Mock}
}

// CreateUserNotification provides a mock function with given fields: ctx, accountId, notification
func (_m *MockUsersApiServicer) CreateUserNotification(ctx context.Context, accountId string, notification coreapi.NotificationType) (coreapi.Notification, error) {
	ret := _m.Called(ctx, accountId, notification)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserNotification")
	}

	var r0 coreapi.Notification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.NotificationType) (coreapi.Notification, error)); ok {
		return rf(ctx, accountId, notification)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, coreapi.NotificationType) coreapi.Notification); ok {
		r0 = rf(ctx, accountId, notification)
	} else {
		r0 = ret.Get(0).(coreapi.Notification)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, coreapi.NotificationType) error); ok {
		r1 = rf(ctx, accountId, notification)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_CreateUserNotification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserNotification'
type MockUsersApiServicer_CreateUserNotification_Call struct {
	*mock.Call
}

// CreateUserNotification is a helper method to define mock.On call
//   - ctx context.Context
//   - accountId string
//   - notification coreapi.NotificationType
func (_e *MockUsersApiServicer_Expecter) CreateUserNotification(ctx interface{}, accountId interface{}, notification interface{}) *MockUsersApiServicer_CreateUserNotification_Call {
	return &MockUsersApiServicer_CreateUserNotification_Call{Call: _e.mock.On("CreateUserNotification", ctx, accountId, notification)}
}

func (_c *MockUsersApiServicer_CreateUserNotification_Call) Run(run func(ctx context.Context, accountId string, notification coreapi.NotificationType)) *MockUsersApiServicer_CreateUserNotification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(coreapi.NotificationType))
	})
	return _c
}

func (_c *MockUsersApiServicer_CreateUserNotification_Call) Return(_a0 coreapi.Notification, _a1 error) *MockUsersApiServicer_CreateUserNotification_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_CreateUserNotification_Call) RunAndReturn(run func(context.Context, string, coreapi.NotificationType) (coreapi.Notification, error)) *MockUsersApiServicer_CreateUserNotification_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUserNotificationById provides a mock function with given fields: ctx, accountId, notificationId
func (_m *MockUsersApiServicer) DeleteUserNotificationById(ctx context.Context, accountId string, notificationId string) error {
	ret := _m.Called(ctx, accountId, notificationId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserNotificationById")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, accountId, notificationId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUsersApiServicer_DeleteUserNotificationById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserNotificationById'
type MockUsersApiServicer_DeleteUserNotificationById_Call struct {
	*mock.Call
}

// DeleteUserNotificationById is a helper method to define mock.On call
//   - ctx context.Context
//   - accountId string
//   - notificationId string
func (_e *MockUsersApiServicer_Expecter) DeleteUserNotificationById(ctx interface{}, accountId interface{}, notificationId interface{}) *MockUsersApiServicer_DeleteUserNotificationById_Call {
	return &MockUsersApiServicer_DeleteUserNotificationById_Call{Call: _e.mock.On("DeleteUserNotificationById", ctx, accountId, notificationId)}
}

func (_c *MockUsersApiServicer_DeleteUserNotificationById_Call) Run(run func(ctx context.Context, accountId string, notificationId string)) *MockUsersApiServicer_DeleteUserNotificationById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_DeleteUserNotificationById_Call) Return(_a0 error) *MockUsersApiServicer_DeleteUserNotificationById_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUsersApiServicer_DeleteUserNotificationById_Call) RunAndReturn(run func(context.Context, string, string) error) *MockUsersApiServicer_DeleteUserNotificationById_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUsersLogout provides a mock function with given fields: _a0, _a1
func (_m *MockUsersApiServicer) DeleteUsersLogout(_a0 context.Context, _a1 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUsersLogout")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (interface{}, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) interface{}); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_DeleteUsersLogout_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUsersLogout'
type MockUsersApiServicer_DeleteUsersLogout_Call struct {
	*mock.Call
}

// DeleteUsersLogout is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockUsersApiServicer_Expecter) DeleteUsersLogout(_a0 interface{}, _a1 interface{}) *MockUsersApiServicer_DeleteUsersLogout_Call {
	return &MockUsersApiServicer_DeleteUsersLogout_Call{Call: _e.mock.On("DeleteUsersLogout", _a0, _a1)}
}

func (_c *MockUsersApiServicer_DeleteUsersLogout_Call) Run(run func(_a0 context.Context, _a1 string)) *MockUsersApiServicer_DeleteUsersLogout_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_DeleteUsersLogout_Call) Return(_a0 interface{}, _a1 error) *MockUsersApiServicer_DeleteUsersLogout_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_DeleteUsersLogout_Call) RunAndReturn(run func(context.Context, string) (interface{}, error)) *MockUsersApiServicer_DeleteUsersLogout_Call {
	_c.Call.Return(run)
	return _c
}

// GetExamInsightsEligibility provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) GetExamInsightsEligibility(_a0 context.Context, _a1 string, _a2 string) (map[string]*coreapi.ExamInsightsEligibility, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetExamInsightsEligibility")
	}

	var r0 map[string]*coreapi.ExamInsightsEligibility
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (map[string]*coreapi.ExamInsightsEligibility, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) map[string]*coreapi.ExamInsightsEligibility); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]*coreapi.ExamInsightsEligibility)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetExamInsightsEligibility_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExamInsightsEligibility'
type MockUsersApiServicer_GetExamInsightsEligibility_Call struct {
	*mock.Call
}

// GetExamInsightsEligibility is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) GetExamInsightsEligibility(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_GetExamInsightsEligibility_Call {
	return &MockUsersApiServicer_GetExamInsightsEligibility_Call{Call: _e.mock.On("GetExamInsightsEligibility", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_GetExamInsightsEligibility_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_GetExamInsightsEligibility_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetExamInsightsEligibility_Call) Return(_a0 map[string]*coreapi.ExamInsightsEligibility, _a1 error) *MockUsersApiServicer_GetExamInsightsEligibility_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetExamInsightsEligibility_Call) RunAndReturn(run func(context.Context, string, string) (map[string]*coreapi.ExamInsightsEligibility, error)) *MockUsersApiServicer_GetExamInsightsEligibility_Call {
	_c.Call.Return(run)
	return _c
}

// GetExamThumbnailByUuid provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockUsersApiServicer) GetExamThumbnailByUuid(_a0 context.Context, _a1 string, _a2 string, _a3 string) (io.ReadCloser, int64, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for GetExamThumbnailByUuid")
	}

	var r0 io.ReadCloser
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (io.ReadCloser, int64, error)); ok {
		return rf(_a0, _a1, _a2, _a3)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) io.ReadCloser); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(io.ReadCloser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) int64); ok {
		r1 = rf(_a0, _a1, _a2, _a3)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, string) error); ok {
		r2 = rf(_a0, _a1, _a2, _a3)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockUsersApiServicer_GetExamThumbnailByUuid_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExamThumbnailByUuid'
type MockUsersApiServicer_GetExamThumbnailByUuid_Call struct {
	*mock.Call
}

// GetExamThumbnailByUuid is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
//   - _a3 string
func (_e *MockUsersApiServicer_Expecter) GetExamThumbnailByUuid(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockUsersApiServicer_GetExamThumbnailByUuid_Call {
	return &MockUsersApiServicer_GetExamThumbnailByUuid_Call{Call: _e.mock.On("GetExamThumbnailByUuid", _a0, _a1, _a2, _a3)}
}

func (_c *MockUsersApiServicer_GetExamThumbnailByUuid_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string, _a3 string)) *MockUsersApiServicer_GetExamThumbnailByUuid_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetExamThumbnailByUuid_Call) Return(_a0 io.ReadCloser, _a1 int64, _a2 error) *MockUsersApiServicer_GetExamThumbnailByUuid_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockUsersApiServicer_GetExamThumbnailByUuid_Call) RunAndReturn(run func(context.Context, string, string, string) (io.ReadCloser, int64, error)) *MockUsersApiServicer_GetExamThumbnailByUuid_Call {
	_c.Call.Return(run)
	return _c
}

// GetNotificationsByAccount provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) GetNotificationsByAccount(_a0 context.Context, _a1 string, _a2 bool) (coreapi.UserNotifications, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetNotificationsByAccount")
	}

	var r0 coreapi.UserNotifications
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) (coreapi.UserNotifications, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) coreapi.UserNotifications); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Get(0).(coreapi.UserNotifications)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetNotificationsByAccount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNotificationsByAccount'
type MockUsersApiServicer_GetNotificationsByAccount_Call struct {
	*mock.Call
}

// GetNotificationsByAccount is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 bool
func (_e *MockUsersApiServicer_Expecter) GetNotificationsByAccount(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_GetNotificationsByAccount_Call {
	return &MockUsersApiServicer_GetNotificationsByAccount_Call{Call: _e.mock.On("GetNotificationsByAccount", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_GetNotificationsByAccount_Call) Run(run func(_a0 context.Context, _a1 string, _a2 bool)) *MockUsersApiServicer_GetNotificationsByAccount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetNotificationsByAccount_Call) Return(_a0 coreapi.UserNotifications, _a1 error) *MockUsersApiServicer_GetNotificationsByAccount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetNotificationsByAccount_Call) RunAndReturn(run func(context.Context, string, bool) (coreapi.UserNotifications, error)) *MockUsersApiServicer_GetNotificationsByAccount_Call {
	_c.Call.Return(run)
	return _c
}

// GetOrganVisualizationByExamId provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) GetOrganVisualizationByExamId(_a0 context.Context, _a1 string, _a2 string) ([]byte, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetOrganVisualizationByExamId")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]byte, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []byte); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetOrganVisualizationByExamId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrganVisualizationByExamId'
type MockUsersApiServicer_GetOrganVisualizationByExamId_Call struct {
	*mock.Call
}

// GetOrganVisualizationByExamId is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) GetOrganVisualizationByExamId(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_GetOrganVisualizationByExamId_Call {
	return &MockUsersApiServicer_GetOrganVisualizationByExamId_Call{Call: _e.mock.On("GetOrganVisualizationByExamId", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_GetOrganVisualizationByExamId_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_GetOrganVisualizationByExamId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetOrganVisualizationByExamId_Call) Return(_a0 []byte, _a1 error) *MockUsersApiServicer_GetOrganVisualizationByExamId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetOrganVisualizationByExamId_Call) RunAndReturn(run func(context.Context, string, string) ([]byte, error)) *MockUsersApiServicer_GetOrganVisualizationByExamId_Call {
	_c.Call.Return(run)
	return _c
}

// GetReportInsights provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) GetReportInsights(_a0 context.Context, _a1 string, _a2 string) (reportinsights.InsightsResponse, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetReportInsights")
	}

	var r0 reportinsights.InsightsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (reportinsights.InsightsResponse, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) reportinsights.InsightsResponse); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Get(0).(reportinsights.InsightsResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetReportInsights_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetReportInsights'
type MockUsersApiServicer_GetReportInsights_Call struct {
	*mock.Call
}

// GetReportInsights is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) GetReportInsights(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_GetReportInsights_Call {
	return &MockUsersApiServicer_GetReportInsights_Call{Call: _e.mock.On("GetReportInsights", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_GetReportInsights_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_GetReportInsights_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetReportInsights_Call) Return(_a0 reportinsights.InsightsResponse, _a1 error) *MockUsersApiServicer_GetReportInsights_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetReportInsights_Call) RunAndReturn(run func(context.Context, string, string) (reportinsights.InsightsResponse, error)) *MockUsersApiServicer_GetReportInsights_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserExamById provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) GetUserExamById(_a0 context.Context, _a1 string, _a2 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetUserExamById")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (interface{}, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) interface{}); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetUserExamById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserExamById'
type MockUsersApiServicer_GetUserExamById_Call struct {
	*mock.Call
}

// GetUserExamById is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) GetUserExamById(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_GetUserExamById_Call {
	return &MockUsersApiServicer_GetUserExamById_Call{Call: _e.mock.On("GetUserExamById", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_GetUserExamById_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_GetUserExamById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetUserExamById_Call) Return(_a0 interface{}, _a1 error) *MockUsersApiServicer_GetUserExamById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetUserExamById_Call) RunAndReturn(run func(context.Context, string, string) (interface{}, error)) *MockUsersApiServicer_GetUserExamById_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserExams provides a mock function with given fields: _a0, _a1, _a2, _a3
func (_m *MockUsersApiServicer) GetUserExams(_a0 context.Context, _a1 string, _a2 bool, _a3 queries.Queries) ([]coreapi.Exam, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3)

	if len(ret) == 0 {
		panic("no return value specified for GetUserExams")
	}

	var r0 []coreapi.Exam
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, queries.Queries) ([]coreapi.Exam, error)); ok {
		return rf(_a0, _a1, _a2, _a3)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool, queries.Queries) []coreapi.Exam); ok {
		r0 = rf(_a0, _a1, _a2, _a3)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]coreapi.Exam)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool, queries.Queries) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetUserExams_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserExams'
type MockUsersApiServicer_GetUserExams_Call struct {
	*mock.Call
}

// GetUserExams is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 bool
//   - _a3 queries.Queries
func (_e *MockUsersApiServicer_Expecter) GetUserExams(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}) *MockUsersApiServicer_GetUserExams_Call {
	return &MockUsersApiServicer_GetUserExams_Call{Call: _e.mock.On("GetUserExams", _a0, _a1, _a2, _a3)}
}

func (_c *MockUsersApiServicer_GetUserExams_Call) Run(run func(_a0 context.Context, _a1 string, _a2 bool, _a3 queries.Queries)) *MockUsersApiServicer_GetUserExams_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool), args[3].(queries.Queries))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetUserExams_Call) Return(_a0 []coreapi.Exam, _a1 error) *MockUsersApiServicer_GetUserExams_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetUserExams_Call) RunAndReturn(run func(context.Context, string, bool, queries.Queries) ([]coreapi.Exam, error)) *MockUsersApiServicer_GetUserExams_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserExamsSize provides a mock function with given fields: _a0, _a1
func (_m *MockUsersApiServicer) GetUserExamsSize(_a0 context.Context, _a1 string) (interface{}, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetUserExamsSize")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (interface{}, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) interface{}); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetUserExamsSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserExamsSize'
type MockUsersApiServicer_GetUserExamsSize_Call struct {
	*mock.Call
}

// GetUserExamsSize is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
func (_e *MockUsersApiServicer_Expecter) GetUserExamsSize(_a0 interface{}, _a1 interface{}) *MockUsersApiServicer_GetUserExamsSize_Call {
	return &MockUsersApiServicer_GetUserExamsSize_Call{Call: _e.mock.On("GetUserExamsSize", _a0, _a1)}
}

func (_c *MockUsersApiServicer_GetUserExamsSize_Call) Run(run func(_a0 context.Context, _a1 string)) *MockUsersApiServicer_GetUserExamsSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetUserExamsSize_Call) Return(_a0 interface{}, _a1 error) *MockUsersApiServicer_GetUserExamsSize_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetUserExamsSize_Call) RunAndReturn(run func(context.Context, string) (interface{}, error)) *MockUsersApiServicer_GetUserExamsSize_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserRequestConsent provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *MockUsersApiServicer) GetUserRequestConsent(_a0 context.Context, _a1 string, _a2 int64, _a3 uint, _a4 string) ([]byte, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for GetUserRequestConsent")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, uint, string) ([]byte, error)); ok {
		return rf(_a0, _a1, _a2, _a3, _a4)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, uint, string) []byte); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int64, uint, string) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetUserRequestConsent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserRequestConsent'
type MockUsersApiServicer_GetUserRequestConsent_Call struct {
	*mock.Call
}

// GetUserRequestConsent is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 int64
//   - _a3 uint
//   - _a4 string
func (_e *MockUsersApiServicer_Expecter) GetUserRequestConsent(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}, _a4 interface{}) *MockUsersApiServicer_GetUserRequestConsent_Call {
	return &MockUsersApiServicer_GetUserRequestConsent_Call{Call: _e.mock.On("GetUserRequestConsent", _a0, _a1, _a2, _a3, _a4)}
}

func (_c *MockUsersApiServicer_GetUserRequestConsent_Call) Run(run func(_a0 context.Context, _a1 string, _a2 int64, _a3 uint, _a4 string)) *MockUsersApiServicer_GetUserRequestConsent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(uint), args[4].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetUserRequestConsent_Call) Return(_a0 []byte, _a1 error) *MockUsersApiServicer_GetUserRequestConsent_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetUserRequestConsent_Call) RunAndReturn(run func(context.Context, string, int64, uint, string) ([]byte, error)) *MockUsersApiServicer_GetUserRequestConsent_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserSettings provides a mock function with given fields: ctx, acctId
func (_m *MockUsersApiServicer) GetUserSettings(ctx context.Context, acctId string) (interface{}, error) {
	ret := _m.Called(ctx, acctId)

	if len(ret) == 0 {
		panic("no return value specified for GetUserSettings")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (interface{}, error)); ok {
		return rf(ctx, acctId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) interface{}); ok {
		r0 = rf(ctx, acctId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, acctId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_GetUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserSettings'
type MockUsersApiServicer_GetUserSettings_Call struct {
	*mock.Call
}

// GetUserSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
func (_e *MockUsersApiServicer_Expecter) GetUserSettings(ctx interface{}, acctId interface{}) *MockUsersApiServicer_GetUserSettings_Call {
	return &MockUsersApiServicer_GetUserSettings_Call{Call: _e.mock.On("GetUserSettings", ctx, acctId)}
}

func (_c *MockUsersApiServicer_GetUserSettings_Call) Run(run func(ctx context.Context, acctId string)) *MockUsersApiServicer_GetUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_GetUserSettings_Call) Return(_a0 interface{}, _a1 error) *MockUsersApiServicer_GetUserSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_GetUserSettings_Call) RunAndReturn(run func(context.Context, string) (interface{}, error)) *MockUsersApiServicer_GetUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// PostFriendReferral provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) PostFriendReferral(_a0 context.Context, _a1 string, _a2 string) error {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostFriendReferral")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUsersApiServicer_PostFriendReferral_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostFriendReferral'
type MockUsersApiServicer_PostFriendReferral_Call struct {
	*mock.Call
}

// PostFriendReferral is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) PostFriendReferral(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_PostFriendReferral_Call {
	return &MockUsersApiServicer_PostFriendReferral_Call{Call: _e.mock.On("PostFriendReferral", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_PostFriendReferral_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_PostFriendReferral_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_PostFriendReferral_Call) Return(_a0 error) *MockUsersApiServicer_PostFriendReferral_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUsersApiServicer_PostFriendReferral_Call) RunAndReturn(run func(context.Context, string, string) error) *MockUsersApiServicer_PostFriendReferral_Call {
	_c.Call.Return(run)
	return _c
}

// PostLoginViaSSO provides a mock function with given fields: ctx, token, accountId, ip
func (_m *MockUsersApiServicer) PostLoginViaSSO(ctx context.Context, token string, accountId string, ip string) (accountservice.AccountSSOLoginResponse, error) {
	ret := _m.Called(ctx, token, accountId, ip)

	if len(ret) == 0 {
		panic("no return value specified for PostLoginViaSSO")
	}

	var r0 accountservice.AccountSSOLoginResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (accountservice.AccountSSOLoginResponse, error)); ok {
		return rf(ctx, token, accountId, ip)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) accountservice.AccountSSOLoginResponse); ok {
		r0 = rf(ctx, token, accountId, ip)
	} else {
		r0 = ret.Get(0).(accountservice.AccountSSOLoginResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, token, accountId, ip)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_PostLoginViaSSO_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostLoginViaSSO'
type MockUsersApiServicer_PostLoginViaSSO_Call struct {
	*mock.Call
}

// PostLoginViaSSO is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - accountId string
//   - ip string
func (_e *MockUsersApiServicer_Expecter) PostLoginViaSSO(ctx interface{}, token interface{}, accountId interface{}, ip interface{}) *MockUsersApiServicer_PostLoginViaSSO_Call {
	return &MockUsersApiServicer_PostLoginViaSSO_Call{Call: _e.mock.On("PostLoginViaSSO", ctx, token, accountId, ip)}
}

func (_c *MockUsersApiServicer_PostLoginViaSSO_Call) Run(run func(ctx context.Context, token string, accountId string, ip string)) *MockUsersApiServicer_PostLoginViaSSO_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_PostLoginViaSSO_Call) Return(_a0 accountservice.AccountSSOLoginResponse, _a1 error) *MockUsersApiServicer_PostLoginViaSSO_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_PostLoginViaSSO_Call) RunAndReturn(run func(context.Context, string, string, string) (accountservice.AccountSSOLoginResponse, error)) *MockUsersApiServicer_PostLoginViaSSO_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsers provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *MockUsersApiServicer) PostUsers(_a0 context.Context, _a1 coreapi.RegisterData, _a2 string, _a3 string, _a4 string) (string, error) {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for PostUsers")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, coreapi.RegisterData, string, string, string) (string, error)); ok {
		return rf(_a0, _a1, _a2, _a3, _a4)
	}
	if rf, ok := ret.Get(0).(func(context.Context, coreapi.RegisterData, string, string, string) string); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, coreapi.RegisterData, string, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_PostUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsers'
type MockUsersApiServicer_PostUsers_Call struct {
	*mock.Call
}

// PostUsers is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 coreapi.RegisterData
//   - _a2 string
//   - _a3 string
//   - _a4 string
func (_e *MockUsersApiServicer_Expecter) PostUsers(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}, _a4 interface{}) *MockUsersApiServicer_PostUsers_Call {
	return &MockUsersApiServicer_PostUsers_Call{Call: _e.mock.On("PostUsers", _a0, _a1, _a2, _a3, _a4)}
}

func (_c *MockUsersApiServicer_PostUsers_Call) Run(run func(_a0 context.Context, _a1 coreapi.RegisterData, _a2 string, _a3 string, _a4 string)) *MockUsersApiServicer_PostUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(coreapi.RegisterData), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_PostUsers_Call) Return(_a0 string, _a1 error) *MockUsersApiServicer_PostUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_PostUsers_Call) RunAndReturn(run func(context.Context, coreapi.RegisterData, string, string, string) (string, error)) *MockUsersApiServicer_PostUsers_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersLoginGoogle provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) PostUsersLoginGoogle(_a0 context.Context, _a1 string, _a2 string) (accountservice.GoogleSSOLoginResponse, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersLoginGoogle")
	}

	var r0 accountservice.GoogleSSOLoginResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (accountservice.GoogleSSOLoginResponse, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) accountservice.GoogleSSOLoginResponse); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Get(0).(accountservice.GoogleSSOLoginResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_PostUsersLoginGoogle_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersLoginGoogle'
type MockUsersApiServicer_PostUsersLoginGoogle_Call struct {
	*mock.Call
}

// PostUsersLoginGoogle is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) PostUsersLoginGoogle(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_PostUsersLoginGoogle_Call {
	return &MockUsersApiServicer_PostUsersLoginGoogle_Call{Call: _e.mock.On("PostUsersLoginGoogle", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_PostUsersLoginGoogle_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_PostUsersLoginGoogle_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_PostUsersLoginGoogle_Call) Return(_a0 accountservice.GoogleSSOLoginResponse, _a1 error) *MockUsersApiServicer_PostUsersLoginGoogle_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_PostUsersLoginGoogle_Call) RunAndReturn(run func(context.Context, string, string) (accountservice.GoogleSSOLoginResponse, error)) *MockUsersApiServicer_PostUsersLoginGoogle_Call {
	_c.Call.Return(run)
	return _c
}

// PostUsersLoginSSO provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) PostUsersLoginSSO(_a0 context.Context, _a1 string, _a2 string) (accountservice.AccountSSOLoginResponse, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for PostUsersLoginSSO")
	}

	var r0 accountservice.AccountSSOLoginResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (accountservice.AccountSSOLoginResponse, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) accountservice.AccountSSOLoginResponse); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Get(0).(accountservice.AccountSSOLoginResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockUsersApiServicer_PostUsersLoginSSO_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PostUsersLoginSSO'
type MockUsersApiServicer_PostUsersLoginSSO_Call struct {
	*mock.Call
}

// PostUsersLoginSSO is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 string
func (_e *MockUsersApiServicer_Expecter) PostUsersLoginSSO(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_PostUsersLoginSSO_Call {
	return &MockUsersApiServicer_PostUsersLoginSSO_Call{Call: _e.mock.On("PostUsersLoginSSO", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_PostUsersLoginSSO_Call) Run(run func(_a0 context.Context, _a1 string, _a2 string)) *MockUsersApiServicer_PostUsersLoginSSO_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_PostUsersLoginSSO_Call) Return(_a0 accountservice.AccountSSOLoginResponse, _a1 error) *MockUsersApiServicer_PostUsersLoginSSO_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockUsersApiServicer_PostUsersLoginSSO_Call) RunAndReturn(run func(context.Context, string, string) (accountservice.AccountSSOLoginResponse, error)) *MockUsersApiServicer_PostUsersLoginSSO_Call {
	_c.Call.Return(run)
	return _c
}

// TriggerOrganVisualizationInference provides a mock function with given fields: _a0, _a1, _a2
func (_m *MockUsersApiServicer) TriggerOrganVisualizationInference(_a0 context.Context, _a1 string, _a2 queries.Queries) error {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for TriggerOrganVisualizationInference")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, queries.Queries) error); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUsersApiServicer_TriggerOrganVisualizationInference_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TriggerOrganVisualizationInference'
type MockUsersApiServicer_TriggerOrganVisualizationInference_Call struct {
	*mock.Call
}

// TriggerOrganVisualizationInference is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 queries.Queries
func (_e *MockUsersApiServicer_Expecter) TriggerOrganVisualizationInference(_a0 interface{}, _a1 interface{}, _a2 interface{}) *MockUsersApiServicer_TriggerOrganVisualizationInference_Call {
	return &MockUsersApiServicer_TriggerOrganVisualizationInference_Call{Call: _e.mock.On("TriggerOrganVisualizationInference", _a0, _a1, _a2)}
}

func (_c *MockUsersApiServicer_TriggerOrganVisualizationInference_Call) Run(run func(_a0 context.Context, _a1 string, _a2 queries.Queries)) *MockUsersApiServicer_TriggerOrganVisualizationInference_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(queries.Queries))
	})
	return _c
}

func (_c *MockUsersApiServicer_TriggerOrganVisualizationInference_Call) Return(_a0 error) *MockUsersApiServicer_TriggerOrganVisualizationInference_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUsersApiServicer_TriggerOrganVisualizationInference_Call) RunAndReturn(run func(context.Context, string, queries.Queries) error) *MockUsersApiServicer_TriggerOrganVisualizationInference_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationAsReadById provides a mock function with given fields: ctx, accountId, notificationId
func (_m *MockUsersApiServicer) UpdateNotificationAsReadById(ctx context.Context, accountId string, notificationId string) error {
	ret := _m.Called(ctx, accountId, notificationId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationAsReadById")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, accountId, notificationId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUsersApiServicer_UpdateNotificationAsReadById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationAsReadById'
type MockUsersApiServicer_UpdateNotificationAsReadById_Call struct {
	*mock.Call
}

// UpdateNotificationAsReadById is a helper method to define mock.On call
//   - ctx context.Context
//   - accountId string
//   - notificationId string
func (_e *MockUsersApiServicer_Expecter) UpdateNotificationAsReadById(ctx interface{}, accountId interface{}, notificationId interface{}) *MockUsersApiServicer_UpdateNotificationAsReadById_Call {
	return &MockUsersApiServicer_UpdateNotificationAsReadById_Call{Call: _e.mock.On("UpdateNotificationAsReadById", ctx, accountId, notificationId)}
}

func (_c *MockUsersApiServicer_UpdateNotificationAsReadById_Call) Run(run func(ctx context.Context, accountId string, notificationId string)) *MockUsersApiServicer_UpdateNotificationAsReadById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUsersApiServicer_UpdateNotificationAsReadById_Call) Return(_a0 error) *MockUsersApiServicer_UpdateNotificationAsReadById_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUsersApiServicer_UpdateNotificationAsReadById_Call) RunAndReturn(run func(context.Context, string, string) error) *MockUsersApiServicer_UpdateNotificationAsReadById_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserSettings provides a mock function with given fields: ctx, acctId, settings
func (_m *MockUsersApiServicer) UpdateUserSettings(ctx context.Context, acctId string, settings accountservice.UserSettingsRequest) error {
	ret := _m.Called(ctx, acctId, settings)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserSettings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, accountservice.UserSettingsRequest) error); ok {
		r0 = rf(ctx, acctId, settings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockUsersApiServicer_UpdateUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserSettings'
type MockUsersApiServicer_UpdateUserSettings_Call struct {
	*mock.Call
}

// UpdateUserSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - acctId string
//   - settings accountservice.UserSettingsRequest
func (_e *MockUsersApiServicer_Expecter) UpdateUserSettings(ctx interface{}, acctId interface{}, settings interface{}) *MockUsersApiServicer_UpdateUserSettings_Call {
	return &MockUsersApiServicer_UpdateUserSettings_Call{Call: _e.mock.On("UpdateUserSettings", ctx, acctId, settings)}
}

func (_c *MockUsersApiServicer_UpdateUserSettings_Call) Run(run func(ctx context.Context, acctId string, settings accountservice.UserSettingsRequest)) *MockUsersApiServicer_UpdateUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(accountservice.UserSettingsRequest))
	})
	return _c
}

func (_c *MockUsersApiServicer_UpdateUserSettings_Call) Return(_a0 error) *MockUsersApiServicer_UpdateUserSettings_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockUsersApiServicer_UpdateUserSettings_Call) RunAndReturn(run func(context.Context, string, accountservice.UserSettingsRequest) error) *MockUsersApiServicer_UpdateUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockUsersApiServicer creates a new instance of MockUsersApiServicer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUsersApiServicer(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUsersApiServicer {
	mock := &MockUsersApiServicer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

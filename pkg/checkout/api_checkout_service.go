package checkout

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	amplitudeEvent "github.com/amplitude/analytics-go/amplitude"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/accounts"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/interfaces"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	enrollments "gitlab.com/pockethealth/coreapi/pkg/mysql/enrollments"
	requests "gitlab.com/pockethealth/coreapi/pkg/mysql/requests"
	scans "gitlab.com/pockethealth/coreapi/pkg/mysql/scans"
	"gitlab.com/pockethealth/coreapi/pkg/orders"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/events"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type CheckoutApiService struct {
	sqldb                *sql.DB
	acctSvcUser          accountservice.AccountService
	planSvc              planservice.PlanService
	orgSvc               orgs.OrgService
	recordSvcClient      recordservice.RecordServiceClientInterface
	cioEventProducer     interfaces.CIOProducerClient
	amplitudeEventClient interfaces.AmplitudeEventClient
}

func NewCheckoutApiService(
	db *sql.DB,
	acctSvcUser accountservice.AccountService,
	ps planservice.PlanService,
	os orgs.OrgService,
	recordSvcClient recordservice.RecordServiceClientInterface,
	cioEventProducer interfaces.CIOProducerClient,
	amplitudeEventClient interfaces.AmplitudeEventClient,
) coreapi.CheckoutApiServicer {
	return &CheckoutApiService{
		sqldb:                db,
		acctSvcUser:          acctSvcUser,
		planSvc:              ps,
		orgSvc:               os,
		recordSvcClient:      recordSvcClient,
		cioEventProducer:     cioEventProducer,
		amplitudeEventClient: amplitudeEventClient,
	}
}

func (s *CheckoutApiService) GetMetadata(
	ctx context.Context,
	providerId int64,
) (*models.CheckoutMetadata, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"provider_id": providerId,
	})

	provider, err := s.orgSvc.GetProviderByLegacyId(ctx, providerId)
	if err != nil {
		lg.WithError(err).Info("could not get provider info")
		return nil, err
	}
	if provider.Logo == "" {
		lg.Infof("could not get provider logo for provider with ID: %s", provider.Id)
	}

	metadata := models.CheckoutMetadata{
		ProviderName: provider.Name,
		Base64Logo:   provider.Logo,
		Region:       provider.Region,
	}

	lg.WithFields(logrus.Fields{
		"has_logo": metadata.Base64Logo != "",
		"region":   metadata.Region,
	}).Info("checkout metadata")

	return &metadata, nil
}

func (s *CheckoutApiService) PostActivateStudies(
	ctx context.Context,
	accountID string,
	activationKey coreapi.ActivationKey,
	IP string,
	deviceID string,
) (coreapi.ActivateStudyResponse, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"account_id": accountID,
	})

	account, err := s.acctSvcUser.GetAccountInfo(ctx, accountID)
	if err != nil {
		lg.WithError(err).Info("could not fetch account info")
		return coreapi.ActivateStudyResponse{}, err
	}

	// Step 1: Fetch all locked studies
	studies, err := s.fetchLockedStudies(ctx, lg, accountID)
	if err != nil {
		lg.WithError(err).Info("could not fetch locked studies for the account")
		return coreapi.ActivateStudyResponse{}, err
	}

	// return early with no error if account has no locked exams
	if len(studies) == 0 {
		return coreapi.ActivateStudyResponse{
			UnlockedStudies: []coreapi.UnlockedStudy{},
		}, nil
	}

	// Step 2: Filter studies by activation key
	filteredStudies, uniqueOrgIDs := s.filterStudiesByActivationKey(studies, activationKey)
	if len(filteredStudies) == 0 {
		lg.WithFields(
			logrus.Fields{
				"locked_studies": len(studies),
			},
		).Info(errormsgs.ERR_NO_RECORDS)
		return coreapi.ActivateStudyResponse{}, errors.New(errormsgs.ERR_NO_RECORDS)
	}

	// Step 3: Check facility-funded status
	facilityFundedMap, allFacilityFunded, err := s.generateFacilityFundedMap(ctx, uniqueOrgIDs)
	if err != nil {
		lg.WithError(err).Info("could not get facility funded status for all providers")
		return coreapi.ActivateStudyResponse{}, err
	}

	// Step 4: Get all active orders
	activeOrders, err := s.acctSvcUser.GetOrders(ctx, accountID, map[string]bool{"active": true})
	if err != nil {
		lg.WithError(err).Info("could not get active orders for the account")
		return coreapi.ActivateStudyResponse{}, err
	}

	if allFacilityFunded {
		return s.activateFacilityFundedStudies(
			ctx,
			lg,
			*account,
			activationKey,
			filteredStudies,
			facilityFundedMap,
			activeOrders,
			IP,
			deviceID,
		)
	}

	generalAccessOrder, err := orders.PickGeneralAccessOrder(ctx, s.planSvc, activeOrders)
	if err != nil {
		lg.WithError(err).Info("could not pick generalAccessOrder")
		return coreapi.ActivateStudyResponse{}, err
	}

	if generalAccessOrder.PlanId == 0 {
		return s.activateStudiesWithBasicPlan(
			ctx,
			lg,
			*account,
			activationKey,
			filteredStudies,
			facilityFundedMap,
			IP,
			deviceID,
		)
	}

	return s.activateStudiesWithOrder(
		ctx,
		lg,
		*account,
		activationKey,
		filteredStudies,
		facilityFundedMap,
		generalAccessOrder,
		IP,
		deviceID,
	)
}

// Helper method to fetch locked studies
func (s *CheckoutApiService) fetchLockedStudies(
	ctx context.Context,
	lg *logrus.Entry,
	accountID string,
) ([]recordservice.PatientStudy, error) {
	studies, err := s.recordSvcClient.GetStudies(
		ctx,
		accountID,
		false,           // includeReports
		false,           // includeInstances
		lo.ToPtr(false), // activated
		[]string{},      // UUIDs
	)
	if err != nil {
		lg.WithError(err).Info("could not fetch locked studies")
		return nil, err
	}
	return studies, nil
}

// Helper method to filter studies by activation key
func (s *CheckoutApiService) filterStudiesByActivationKey(
	studies []recordservice.PatientStudy,
	activationKey coreapi.ActivationKey,
) ([]recordservice.PatientStudy, map[int]struct{}) {
	var filteredStudies []recordservice.PatientStudy
	uniqueOrgIDs := make(map[int]struct{})

	for _, study := range studies {
		if study.DicomPatientTags.PatientBirthDate == activationKey.DateOfBirth {
			filteredStudies = append(filteredStudies, study)
			uniqueOrgIDs[int(study.OrganizationID)] = struct{}{}
		}
	}

	return filteredStudies, uniqueOrgIDs
}

// Helper method to activate facility-funded studies
func (s *CheckoutApiService) activateFacilityFundedStudies(
	ctx context.Context,
	lg *logrus.Entry,
	account accountservice.Account,
	activationKey coreapi.ActivationKey,
	studies []recordservice.PatientStudy,
	facilityFundedMap map[int]bool,
	activeOrders []accountservice.Order,
	IP string,
	deviceID string,
) (coreapi.ActivateStudyResponse, error) {
	var orderID string

	// if an active FF order exists use that for activation
	for _, order := range activeOrders {
		if order.PlanId == accountservice.FACILITY_FUNDED_PLAN {
			orderID = order.OrderId
		}
	}

	// create a new order if no active FF order exists
	if orderID == "" {
		order, err := orders.HandleGenericOrder(
			ctx,
			s.acctSvcUser,
			account.AccountId,
			accountservice.FACILITY_FUNDED_PLAN,
			"",    // paymentToken
			0,     // providerID
			"",    // requestID
			false, // requiresPayment
			false, // disableAutoRenew
			"",    // source - can be empty when there's no payment
		)
		if err != nil {
			lg.WithError(err).Info("could not create facility-funded plan")
			return coreapi.ActivateStudyResponse{}, err
		}

		orderID = order.OrderId
	}

	studyAvailabilityStatuses, err := s.generateStudyAvailabilityStatuses(
		ctx,
		lg,
		studies,
		facilityFundedMap,
		true,
		"",
	)
	if err != nil {
		lg.WithError(err).Info("error generating study availability map")
		return coreapi.ActivateStudyResponse{}, err
	}
	activatedStudies, err := s.recordSvcClient.PostActivateStudies(
		ctx,
		account.AccountId,
		activationKey,
		studyAvailabilityStatuses,
		orderID,
	)
	if err != nil {
		lg.WithError(err).Info("could not activate facility-funded studies")
		return coreapi.ActivateStudyResponse{}, err
	}
	return s.computeActivateStudyResponse(
		ctx,
		lg,
		activatedStudies,
		account,
		accountservice.FACILITY_FUNDED_PLAN,
		IP,
		deviceID,
	)
}

// Helper method to activate studies with a basic plan
func (s *CheckoutApiService) activateStudiesWithBasicPlan(
	ctx context.Context,
	lg *logrus.Entry,
	account accountservice.Account,
	activationKey coreapi.ActivationKey,
	studies []recordservice.PatientStudy,
	facilityFundedMap map[int]bool,
	IP string,
	deviceID string,
) (coreapi.ActivateStudyResponse, error) {
	order, err := orders.HandleGenericOrder(
		ctx,
		s.acctSvcUser,
		account.AccountId,
		accountservice.BASIC_PLAN,
		"PH_BASIC", // paymentToken
		0,          // providerID
		"",         // requestID
		false,      // requiresPayment
		false,      // disableAutoRenew
		"",         //  source - can be empty when there's no payment
	)
	if err != nil {
		lg.WithError(err).Info("could not create basic plan order")
		return coreapi.ActivateStudyResponse{}, err
	}

	studyAvailabilityStatuses, err := s.generateStudyAvailabilityStatuses(
		ctx,
		lg,
		studies,
		facilityFundedMap,
		false,
		"",
	)
	if err != nil {
		lg.WithError(err).Info("error generating study availability map")
		return coreapi.ActivateStudyResponse{}, err
	}

	activatedStudies, err := s.recordSvcClient.PostActivateStudies(
		ctx,
		account.AccountId,
		activationKey,
		studyAvailabilityStatuses,
		order.OrderId,
	)
	if err != nil {
		lg.WithError(err).Info("could not activate studies with basic plan")
		return coreapi.ActivateStudyResponse{}, err
	}

	return s.computeActivateStudyResponse(
		ctx,
		lg,
		activatedStudies,
		account,
		accountservice.BASIC_PLAN,
		IP,
		deviceID,
	)
}

// Helper method to activate studies with an active order
func (s *CheckoutApiService) activateStudiesWithOrder(
	ctx context.Context,
	lg *logrus.Entry,
	account accountservice.Account,
	activationKey coreapi.ActivationKey,
	studies []recordservice.PatientStudy,
	facilityFundedMap map[int]bool,
	activeOrder accountservice.Order,
	IP string,
	deviceID string,
) (coreapi.ActivateStudyResponse, error) {
	studyAvailabilityStatus, err := s.computeStudyAvailabilityStatus(
		ctx,
		int32(activeOrder.PlanId), // #nosec G115 not worrying about max_int32
	)
	if err != nil {
		lg.WithError(err).Info("could not compute study availability status")
		return coreapi.ActivateStudyResponse{}, err
	}

	studyAvailabilityStatuses, err := s.generateStudyAvailabilityStatuses(
		ctx,
		lg,
		studies,
		facilityFundedMap,
		false,
		studyAvailabilityStatus,
	)
	if err != nil {
		lg.WithError(err).Info("error generating study availability map")
		return coreapi.ActivateStudyResponse{}, err
	}

	activatedStudies, err := s.recordSvcClient.PostActivateStudies(
		ctx,
		account.AccountId,
		activationKey,
		studyAvailabilityStatuses,
		activeOrder.OrderId,
	)
	if err != nil {
		lg.WithError(err).Info("could not activate studies with active order")
		return coreapi.ActivateStudyResponse{}, err
	}

	return s.computeActivateStudyResponse(
		ctx,
		lg,
		activatedStudies,
		account,
		activeOrder.PlanId,
		IP,
		deviceID,
	)
}

// generateFacilityFundedMap generates a map indicating whether each provider ID
// in the input set is facility-funded and determines if all provider IDs belong
// to facility-funded providers.
//
// Parameters:
//   - ctx: The context for controlling cancellation and deadlines for the operation.
//   - providerIDs: A map with provider IDs as keys and empty structs as values,
//     representing the set of provider IDs to check.
//
// Returns:
//   - map[int]bool: A map where the keys are provider IDs and the values are booleans
//     indicating whether the provider is facility-funded (true) or not (false).
//   - bool: A boolean indicating whether all provider IDs in the input set are facility-funded.
//   - error: An error if any issue occurs during the processing (e.g., a failure
//     in the IsFacilityFunded function).
//
// If an error is encountered during the execution, the function returns the
// partially constructed map, a false value for the all-facility-funded boolean,
// and the error. Otherwise, the function completes successfully with the map
// and boolean result.
func (s *CheckoutApiService) generateFacilityFundedMap(
	ctx context.Context,
	providerIDs map[int]struct{},
) (map[int]bool, bool, error) {
	facilityFundedMap := make(map[int]bool)
	allFacilityFunded := true
	for id := range providerIDs {
		isFacilityFunded, err := s.orgSvc.IsFacilityFunded(
			ctx,
			uint(id),
		) // #nosec G115 provider IDs > 0
		if err != nil {
			logutils.CtxLogger(ctx).
				WithError(err).
				Infof("could not get facility funded existence for providerID: %d", id)
			return facilityFundedMap, false, err
		}
		facilityFundedMap[id] = isFacilityFunded
		if !isFacilityFunded {
			allFacilityFunded = false
		}
	}
	return facilityFundedMap, allFacilityFunded, nil
}

func (s *CheckoutApiService) generateStudyAvailabilityStatuses(
	ctx context.Context,
	lg *logrus.Entry,
	studies []recordservice.PatientStudy,
	facilityFundedMap map[int]bool,
	allFacilityFunded bool,
	availabilityAccess models.UnlockStatus,
) (map[string]models.UnlockStatus, error) {
	// Initialize the result map
	studyAvailabilityStatuses := make(map[string]models.UnlockStatus)

	// Determine the default access level
	var defaultAccess models.UnlockStatus
	if availabilityAccess != "" {
		defaultAccess = availabilityAccess
	} else {
		defaultAccess = models.EXAM_LIMITED_AVAILABILITY
	}

	if allFacilityFunded {
		defaultAccess = models.EXAM_FULL_ACCESS
	}

	for _, study := range studies {
		accessLevel := defaultAccess
		if !allFacilityFunded && facilityFundedMap[int(study.OrganizationID)] {
			accessLevel = models.EXAM_FULL_ACCESS
		}

		// These should be only true for Provider Outreach plan that sets attribution before exam unlock.
		if study.AvailabilityStatus != recordservice.PatientStudyAvailabilityStatus(
			models.EXAM_LOCKED,
		) &&
			study.AvailabilityStatus != "" {
			accessLevel, err := s.computeAccessLevel(
				lg,
				accessLevel,
				models.UnlockStatus(study.AvailabilityStatus),
			)
			if err != nil {
				lg.WithError(err).Info("could not compute study access level")
				return studyAvailabilityStatuses, err
			}

			studyAvailabilityStatuses[study.UUID] = accessLevel
		} else if study.OrderID != "" {
			order, err := s.acctSvcUser.GetOrderForId(ctx, study.OrderID)
			if err != nil {
				lg.WithError(err).Info("could not get order for ID: ", study.OrderID)
				return studyAvailabilityStatuses, err
			}

			if order.PlanId == 0 {
				lg.Info("could not get order planID")
				return studyAvailabilityStatuses, fmt.Errorf("could not get order planID")
			}

			attributedAccess, err := s.computeStudyAvailabilityStatus(ctx, int32(order.PlanId)) // #nosec G115 not worrying yet about max_int32 plan ID
			if err != nil {
				lg.WithError(err).Info("could not compute study availability status")
				return studyAvailabilityStatuses, err
			}

			accessLevel, err := s.computeAccessLevel(
				lg,
				accessLevel,
				attributedAccess,
			)
			if err != nil {
				lg.WithError(err).Info("could not compute study access level")
				return studyAvailabilityStatuses, err
			}

			studyAvailabilityStatuses[study.UUID] = accessLevel
		} else {
			studyAvailabilityStatuses[study.UUID] = accessLevel
		}
	}

	return studyAvailabilityStatuses, nil
}

func (s *CheckoutApiService) computeAccessLevel(
	lg *logrus.Entry,
	generalAccess models.UnlockStatus,
	attributedAccess models.UnlockStatus,
) (models.UnlockStatus, error) {
	if ok, err := models.HasGreaterStudyAccessLevel(generalAccess, attributedAccess); err != nil {
		lg.WithError(err).Info("could not compute greater study access")
		return "", err
	} else if ok {
		return generalAccess, nil
	}
	return attributedAccess, nil
}

func (s *CheckoutApiService) computeStudyAvailabilityStatus(
	ctx context.Context,
	planID int32,
) (models.UnlockStatus, error) {
	plan, err := s.planSvc.GetPlanById(ctx, planID)
	if err != nil {
		logutils.CtxLogger(ctx).WithError(err).Info("could not get plan for the provided planID")
		return "", nil
	}

	if plan.GeneralProviderAccess && plan.FullUnlockAccess {
		return models.EXAM_FULL_ACCESS, nil
	} else {
		return models.EXAM_LIMITED_AVAILABILITY, nil
	}
}

func (s *CheckoutApiService) computeActivateStudyResponse(
	ctx context.Context,
	lg *logrus.Entry,
	activatedStudies []recordservice.PatientStudy,
	account accountservice.Account,
	planID uint64,
	IP string,
	deviceID string,
) (coreapi.ActivateStudyResponse, error) {
	providerMap := map[int64]orgs.Provider{}
	for _, study := range activatedStudies {
		if _, ok := providerMap[study.OrganizationID]; !ok {
			provider, err := s.orgSvc.GetProviderByLegacyId(ctx, study.OrganizationID)
			if err != nil {
				logutils.CtxLogger(ctx).
					WithError(err).
					Error("cannot get provider by orgID", study.OrganizationID)
			} else {
				providerMap[study.OrganizationID] = provider
			}
		}
	}
	var unlockedStudies []coreapi.UnlockedStudy
	for _, study := range activatedStudies {
		unlockedStudy := study.ToUnlockedStudy()
		if provider, ok := providerMap[study.OrganizationID]; ok {
			unlockedStudy.OrgName = provider.Name
		}
		unlockedStudies = append(unlockedStudies, unlockedStudy)
	}

	accessToken, err := auth.MakeChallengeUnlockToken(
		"", // transferID
		account.AccountId,
		IP,
	)
	if err != nil {
		lg.WithError(err).Error("could not generate access token")
		return coreapi.ActivateStudyResponse{}, err
	}

	passwordSetupToken, err := s.getPasswordSetupToken(
		ctx,
		account,
		activatedStudies[0].OrganizationID,
	)
	if err != nil {
		lg.WithError(err).Error("failed to get password setup token")
		return coreapi.ActivateStudyResponse{}, err
	}

	go s.createOrUpdatePatientPII(ctx, account.AccountId, activatedStudies)

	orgIDs := lo.Uniq(
		lo.Map(activatedStudies, func(study recordservice.PatientStudy, _ int) int64 {
			return study.OrganizationID
		}),
	)
	err = s.cioEventProducer.ProduceEvent(
		account.AccountId,
		string(events.RecordsActivated),
		map[string]interface{}{"orgIDs": orgIDs})
	if err != nil {
		lg.WithError(err).Error("Failed to send records activated CIO event")
	}

	err = s.cioEventProducer.ProduceEvent(
		account.AccountId,
		string(events.NewRecordAvailable),
		map[string]interface{}{"activated": true})
	if err != nil {
		lg.WithError(err).Error("Failed to send new records available CIO event")
	}

	err = s.cioEventProducer.ProduceEvent(
		account.AccountId,
		string(events.NewTransferAvailable),
		map[string]interface{}{"activated": true})
	if err != nil {
		lg.WithError(err).Error("Could not send activation event to CIO")
	}

	s.amplitudeEventClient.Track(amplitudeEvent.Event{
		EventType:       "new record available",
		UserID:          account.AccountId,
		DeviceID:        deviceID,
		EventProperties: map[string]any{"activated": true},
	})

	s.amplitudeEventClient.Track(amplitudeEvent.Event{
		EventType:       "new transfer available",
		UserID:          account.AccountId,
		DeviceID:        deviceID,
		EventProperties: map[string]any{"activated": true},
	})

	return coreapi.ActivateStudyResponse{
		UnlockedStudies:    unlockedStudies,
		AccessToken:        accessToken,
		PasswordResetToken: passwordSetupToken,
		PlanId:             planID,
	}, nil
}

func (s *CheckoutApiService) createOrUpdatePatientPII(
	ctx context.Context,
	accountID string,
	studies []recordservice.PatientStudy,
) {
	lg := logutils.DebugCtxLogger(ctx).WithField("accountID", accountID)
	uniqueOrgIDStudies := filterUniqueOrgIDStudies(studies)

	for _, study := range uniqueOrgIDStudies {
		enrollment, request, err := s.fetchEnrollmentAndRequest(ctx, accountID, study)
		if err != nil {
			lg.WithError(err).Error("failed to fetch enrollment or request")
			continue
		}

		patientID := s.resolvePatientID(study, enrollment, request)
		if patientID == "" {
			patientID, err = s.createPatient(ctx, accountID, enrollment, request, study)
			if err != nil {
				lg.WithError(err).Error("failed to create patient")
				continue
			}
		} else {
			err = s.updatePatient(ctx, accountID, patientID, study)
			if err != nil {
				lg.WithError(err).Error("failed to update patient")
				continue
			}
		}

		err = s.attributePatient(ctx, enrollment, request, study, patientID)
		if err != nil {
			lg.WithFields(logrus.Fields{
				"enrollment_id": enrollment.Id,
				"patient_id":    patientID,
				"request_id":    request.RequestId,
			}).WithError(err).Error("error attributing patient")
		}
	}
}

func filterUniqueOrgIDStudies(studies []recordservice.PatientStudy) []recordservice.PatientStudy {
	orgIDSet := make(map[int64]bool)
	uniqueOrgIDStudies := []recordservice.PatientStudy{}

	for _, study := range studies {
		if !orgIDSet[study.OrganizationID] {
			orgIDSet[study.OrganizationID] = true
			uniqueOrgIDStudies = append(uniqueOrgIDStudies, study)
		}
	}

	return uniqueOrgIDStudies
}

func (s *CheckoutApiService) fetchEnrollmentAndRequest(
	ctx context.Context,
	accountID string,
	study recordservice.PatientStudy,
) (coreapi.Enrollment, models.Request, error) {
	enrollment, err := enrollments.GetEnrollment(
		ctx, s.sqldb, accountID, study.OrganizationID, study.DicomPatientTags.PatientID,
	)
	if err != nil {
		return coreapi.Enrollment{}, models.Request{}, err
	}

	var request models.Request
	// there may not be a request for consent flow or direct enrollment
	if enrollment.RequestId > 0 {
		request, err = requests.GetRequestById(ctx, s.sqldb, enrollment.RequestId)
		if err != nil {
			return enrollment, models.Request{}, err
		}
	}

	return enrollment, request, nil
}

func (s *CheckoutApiService) resolvePatientID(
	study recordservice.PatientStudy,
	enrollment coreapi.Enrollment,
	request models.Request,
) string {
	if study.PatientID != "" {
		return study.PatientID
	} else if enrollment.PatientId != "" {
		return enrollment.PatientId
	} else if request.PatientId != "" {
		return request.PatientId
	}
	return ""
}

func (s *CheckoutApiService) createPatient(
	ctx context.Context,
	accountID string,
	enrollment coreapi.Enrollment,
	request models.Request,
	study recordservice.PatientStudy,
) (string, error) {
	parsedDOB, _ := time.Parse(coreapi.DICOMDateFormat, study.DicomPatientTags.PatientBirthDate)
	isoDOB := parsedDOB.Format("2006-01-02")
	examRaw, err := study.ToExamRaw()
	if err != nil {
		return "", err
	}

	formattedName := examRaw.GetFormattedPatientName(ctx)

	return s.acctSvcUser.GetOrCreatePatient(
		ctx,
		accountID,
		accountservice.Patient{
			Email:       enrollment.Email,
			FirstName:   formattedName.FirstAndMiddleName,
			LastName:    formattedName.LastName,
			AltLastName: request.AltLastName,
			DOB:         isoDOB,
			Phone:       study.DicomPatientTags.PatientTelephoneNumber,
			Sex:         study.DicomPatientTags.PatientSex,
			Source:      accountservice.PATIENT_CREATION_EXAM_ACTIVATION,
		},
		false,
	)
}

func (s *CheckoutApiService) updatePatient(
	ctx context.Context,
	accountID string,
	patientID string,
	study recordservice.PatientStudy,
) error {
	parsedDOB, _ := time.Parse(coreapi.DICOMDateFormat, study.DicomPatientTags.PatientBirthDate)
	isoDOB := parsedDOB.Format("2006-01-02")
	examRaw, _ := study.ToExamRaw()
	formattedName := examRaw.GetFormattedPatientName(ctx)

	patient, err := s.acctSvcUser.GetPatient(ctx, accountID, patientID)
	if err != nil {
		return err
	}

	patient.AccountId = accountID
	updates := accountservice.Patient{
		DOB:       isoDOB,
		Phone:     study.DicomPatientTags.PatientTelephoneNumber,
		FirstName: formattedName.FirstAndMiddleName,
		LastName:  formattedName.LastName,
		Sex:       study.DicomPatientTags.PatientSex,
	}

	accounts.UpdateMissingPatientPII(ctx, s.acctSvcUser, patient, updates)
	return nil
}

func (s *CheckoutApiService) attributePatient(
	ctx context.Context,
	enrollment coreapi.Enrollment,
	request models.Request,
	study recordservice.PatientStudy,
	patientID string,
) error {
	if enrollment.PatientId == "" {
		if err := enrollments.AttributePatient(ctx, s.sqldb, enrollment.Id, patientID); err != nil {
			return err
		}
	}

	if request.RequestId > 0 && request.PatientId == "" {
		if err := requests.AttributePatient(ctx, s.sqldb, request.RequestId, patientID); err != nil {
			return err
		}
	}

	if study.PatientID == "" && study.TransferID != "" {
		if err := scans.AttributePatient(ctx, s.sqldb, study.TransferID, patientID); err != nil {
			return err
		}
	}

	return nil
}

func (s *CheckoutApiService) getPasswordSetupToken(
	ctx context.Context,
	account accountservice.Account,
	providerId int64,
) (string, error) {
	lg := logutils.DebugCtxLogger(ctx).WithField("account_id", account.AccountId)

	if !account.IsPassSet && !s.acctSvcUser.IsSSOEnabled(ctx, account.AccountId) &&
		!enrollments.IsSSOEnrolled(ctx, s.sqldb, account.AccountId, providerId) {
		verificationToken, err := s.acctSvcUser.GetPasswordSetupVerificationToken(
			ctx,
			account.AccountId,
		)
		if err != nil {
			lg.WithError(err).Error("failed to fetch account verification token")
			return "", err
		}
		return verificationToken.Token, nil
	}

	return "", nil
}

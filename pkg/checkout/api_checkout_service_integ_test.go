//go:build integration
// +build integration

package checkout

import (
	"context"
	"net/http"
	"testing"

	"github.com/amplitude/analytics-go/amplitude"
	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/phutils/v10/pkg/events"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"go.uber.org/mock/gomock"

	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/mocks"
	"gitlab.com/pockethealth/coreapi/pkg/regions"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	mock_accountservice "gitlab.com/pockethealth/coreapi/pkg/services/accountservice/mock"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	mock_orgs "gitlab.com/pockethealth/coreapi/pkg/services/orgs/mock"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	mock_plans "gitlab.com/pockethealth/coreapi/pkg/services/plans/mock"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
)

var (
	acctSvcUser   = "core-dev"
	acctSvcApiKey = "o5h2VvckcTZxX7rAaAMpEOdhpShC7071XFkESUD4KklgJXNnD3ZWjPC8ZEdnm3hp"
)

var (
	planSvcUser   = "core-qa"
	planSvcApiKey = "oB7Ud0WOsXH2UgZ+0S/CvoBRk8gWtGXNeDfC7ez0e4w="
)

func TestGetCheckoutMetadata(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	acctSvcUrl := cfg.AcctSvcUrl
	db := testutils.SetupTestDB(t)
	regions.SetRegionID(1)

	as := accountservice.AccountServiceClient{
		AccountServiceUrl:    acctSvcUrl,
		AccountServiceUser:   acctSvcUser,
		AccountServiceAPIKey: acctSvcApiKey,
		HttpClient: httpclient.NewHTTPClient(
			&http.Client{},
			nil,
		),
	}
	ps := planservice.PlanServiceClient{
		PlanServiceUrl:    cfg.PlanSvcUrl,
		PlanServiceUser:   planSvcUser,
		PlanServiceAPIKey: planSvcApiKey,
		HttpClient: httpclient.NewHTTPClient(
			&http.Client{},
			nil,
		),
	}
	os := orgs.OrgServiceClient{
		OrgServiceUrl:    cfg.OrgSvcUrl,
		OrgServiceUser:   cfg.OrgSvcUser,
		OrgServiceAPIKey: cfg.OrgSvcApiKey,
		HttpClient: httpclient.NewHTTPClient(
			&http.Client{},
			nil,
		),
	}

	s := CheckoutApiService{
		sqldb:       db,
		acctSvcUser: &as,
		orgSvc:      &os,
		planSvc:     &ps,
	}

	t.Run("when account and provider exists, returns checkout metadata", func(t *testing.T) {
		metadata, err := s.GetMetadata(
			context.Background(),
			11,
		)

		require.NoError(t, err, "expected no error, got %v", err)
		assert.NotNil(t, metadata)
		assert.Equal(t, "Oxford Medical Imaging", metadata.ProviderName)
		assert.Equal(t, "CA", metadata.Region)
	})

	t.Run("when provider does not exist, returns nil", func(t *testing.T) {
		metadata, err := s.GetMetadata(
			context.Background(),
			9999,
		)

		require.Error(t, err)
		assert.Nil(t, metadata)
	})

	t.Run(
		"when account does not have plan covering provider, returns checkout metadata with empty plan id",
		func(t *testing.T) {
			metadata, err := s.GetMetadata(
				context.Background(),
				85,
			)

			require.NoError(t, err, "expected no error, got %v", err)
			assert.NotNil(t, metadata)
			assert.Equal(t, "Trillium Health Partners", metadata.ProviderName)
		},
	)
}

func TestPostActivateStudiesServicer(t *testing.T) {
	t.Run("no studies to unlock", func(t *testing.T) {
		s, mockAccountService, _, _, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(t)
		defer ctrl.Finish()
		accountID := "account456"
		deviceID := "device123"
		activationKey := coreapi.ActivationKey{
			DateOfBirth: "********",
		}
		IP := "mock-IP"

		// Mock fetching account info
		mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
			&accountservice.Account{
				Email: "<EMAIL>",
			}, nil,
		)

		// Mock fetching locked studies
		mockRecordService.EXPECT().
			GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
			Return([]recordservice.PatientStudy{}, nil)

		_, err := s.PostActivateStudies(context.Background(), accountID, activationKey, IP, deviceID)
		assert.Nil(t, err)
		assert.Empty(t, mockCIOEventProducer.CallLogs)
		assert.Empty(t, mockAmplitudeEventClient.EventLogs)
	})

	t.Run(
		"successful activation with facility-funded studies - no prior order",
		func(t *testing.T) {
			s, mockAccountService, mockOrgService, _, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
				t,
			)
			defer ctrl.Finish()
			accountID := "mock-accountID"
			deviceID := "device123"
			activationKey := coreapi.ActivationKey{
				DateOfBirth: "********",
			}
			IP := "mock-IP"
			organizationID := int64(1)

			// Mock fetching account info
			mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
				&accountservice.Account{
					AccountId: accountID,
					Email:     "<EMAIL>",
					IsPassSet: true,
				}, nil,
			)

			// Mock fetching locked studies
			mockRecordService.EXPECT().
				GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "study123",
							OrganizationID: organizationID,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
					}, nil,
				)

			// Mock facility-funded map
			mockOrgService.EXPECT().
				IsFacilityFunded(gomock.Any(), uint(organizationID)).
				Return(true, nil)

			// Mock fetching active orders
			mockAccountService.EXPECT().
				GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
				Return(
					[]accountservice.Order{}, nil,
				)

			// Mock Creating Order
			mockAccountService.EXPECT().
				CreateOrder(gomock.Any(), gomock.Any(), accountservice.NewOrder{
					PlanId:       uint64(accountservice.FACILITY_FUNDED_PLAN),
					OrgId:        0,
					RegionId:     regions.GetRegionID(),
					PaymentToken: "noop",
					Country:      regions.GetRegion(),
					ZipCode:      "A1A1A1",
				}).
				Return(
					accountservice.CreateOrderResponse{OrderId: "order123"}, nil,
				)

			// Mock activating studies
			mockRecordService.EXPECT().
				PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "order123").
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "study123",
							OrganizationID: organizationID,
						},
					}, nil,
				)

			// Mock GetProviderByLegacyId
			mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), organizationID).Return(
				orgs.Provider{
					Name: "mock-provider",
				}, nil,
			)

			response, err := s.PostActivateStudies(
				context.Background(),
				accountID,
				activationKey,
				IP,
				deviceID,
			)
			assert.NoError(t, err)
			assert.Equal(t, 1, len(response.UnlockedStudies))
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
			assert.EqualValues(
				t,
				events.RecordsActivated,
				mockCIOEventProducer.CallLogs[0]["eventName"],
			)
			assert.ElementsMatch(
				t,
				[]int64{organizationID},
				mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"],
			)
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
			assert.EqualValues(
				t,
				events.NewRecordAvailable,
				mockCIOEventProducer.CallLogs[1]["eventName"],
			)
			assert.True(
				t,
				mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
			)

			assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
			assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
		},
	)

	t.Run(
		"successful activation with facility-funded studies - with prior order",
		func(t *testing.T) {
			s, mockAccountService, mockOrgService, _, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
				t,
			)
			defer ctrl.Finish()
			accountID := "mock-accountID"
			deviceID := "device123"
			activationKey := coreapi.ActivationKey{
				DateOfBirth: "********",
			}
			IP := "mock-IP"
			organizationID := int64(1)

			// Mock fetching account info
			mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
				&accountservice.Account{
					AccountId: accountID,
					Email:     "<EMAIL>",
					IsPassSet: true,
				}, nil,
			)

			// Mock fetching locked studies
			mockRecordService.EXPECT().
				GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "study123",
							OrganizationID: organizationID,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
					}, nil,
				)

			// Mock facility-funded map
			mockOrgService.EXPECT().
				IsFacilityFunded(gomock.Any(), uint(organizationID)).
				Return(true, nil)

			// Mock fetching active orders
			mockAccountService.EXPECT().
				GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
				Return(
					[]accountservice.Order{
						{
							OrderId: "order123",
							PlanId:  10,
						},
					}, nil,
				)

			// Mock activating studies
			mockRecordService.EXPECT().
				PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "order123").
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "study123",
							OrganizationID: organizationID,
						},
					}, nil,
				)

			// Mock GetProviderByLegacyId
			mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), organizationID).Return(
				orgs.Provider{
					Name: "mock-provider",
				}, nil,
			)

			response, err := s.PostActivateStudies(
				context.Background(),
				accountID,
				activationKey,
				IP,
				deviceID,
			)
			assert.NoError(t, err)
			assert.Equal(t, 1, len(response.UnlockedStudies))
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
			assert.EqualValues(
				t,
				events.RecordsActivated,
				mockCIOEventProducer.CallLogs[0]["eventName"],
			)
			assert.ElementsMatch(
				t,
				[]int64{organizationID},
				mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"],
			)
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
			assert.EqualValues(
				t,
				events.NewRecordAvailable,
				mockCIOEventProducer.CallLogs[1]["eventName"],
			)
			assert.True(
				t,
				mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
			)
			assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
			assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
		},
	)

	t.Run("successful activation with basic plan", func(t *testing.T) {
		s, mockAccountService, mockOrgService, _, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
			t,
		)
		defer ctrl.Finish()
		accountID := "accountBasic123"
		deviceID := "device123"
		activationKey := coreapi.ActivationKey{
			DateOfBirth: "********",
		}
		IP := "mock-IP"
		organizationID := int64(2)

		// Mock fetching account info
		mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
			&accountservice.Account{
				AccountId: accountID,
				Email:     "<EMAIL>",
				IsPassSet: true,
			}, nil,
		)

		// Mock fetching locked studies
		mockRecordService.EXPECT().
			GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyBasic123",
						OrganizationID: organizationID,
						DicomPatientTags: recordservice.DicomPatientTags{
							PatientBirthDate: "********",
						},
						AvailabilityStatus: recordservice.NO_AVAILABILITY,
					},
				}, nil,
			)

		// Mock facility-funded map
		mockOrgService.EXPECT().
			IsFacilityFunded(gomock.Any(), uint(organizationID)).
			Return(false, nil)

		// Mock getting order
		mockAccountService.EXPECT().
			GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
			Return(
				[]accountservice.Order{}, nil,
			)

		// Mock creating order
		mockAccountService.EXPECT().CreateOrder(gomock.Any(), gomock.Any(), accountservice.NewOrder{
			PlanId:       uint64(accountservice.BASIC_PLAN),
			OrgId:        0,
			RegionId:     regions.GetRegionID(),
			PaymentToken: "PH_BASIC",
			Country:      regions.GetRegion(),
			ZipCode:      "A1A1A1",
		}).Return(
			accountservice.CreateOrderResponse{OrderId: "basicOrder123"}, nil,
		)

		// Mock activating studies
		mockRecordService.EXPECT().
			PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "basicOrder123").
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyBasic123",
						OrganizationID: organizationID,
					},
				}, nil,
			)
		// Mock GetProviderByLegacyId
		mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), int64(organizationID)).Return(
			orgs.Provider{
				Name: "mock-provider",
			}, nil,
		)

		response, err := s.PostActivateStudies(context.Background(), accountID, activationKey, IP, deviceID)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(response.UnlockedStudies))
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
		assert.EqualValues(
			t,
			events.RecordsActivated,
			mockCIOEventProducer.CallLogs[0]["eventName"],
		)
		assert.ElementsMatch(t, []int64{organizationID},
			mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"])
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
		assert.EqualValues(
			t,
			events.NewRecordAvailable,
			mockCIOEventProducer.CallLogs[1]["eventName"],
		)
		assert.True(
			t,
			mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
		)
		assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
		assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
	})

	t.Run("successful activation with active order - Unlimited", func(t *testing.T) {
		s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
			t,
		)
		defer ctrl.Finish()
		accountID := "accountActive123"
		deviceID := "device123"
		activationKey := coreapi.ActivationKey{
			DateOfBirth: "********",
		}
		IP := "mock-IP"
		organizationID := int64(3)

		// Mock fetching account info
		mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
			&accountservice.Account{
				AccountId: accountID,
				Email:     "<EMAIL>",
				IsPassSet: true,
			}, nil,
		)

		// Mock fetching locked studies
		mockRecordService.EXPECT().
			GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyActive123",
						OrganizationID: organizationID,
						DicomPatientTags: recordservice.DicomPatientTags{
							PatientBirthDate: "********",
						},
						AvailabilityStatus: recordservice.NO_AVAILABILITY,
					},
				}, nil,
			)

		// Mock facility-funded map
		mockOrgService.EXPECT().
			IsFacilityFunded(gomock.Any(), uint(organizationID)).
			Return(false, nil)

		// Mock fetching active orders
		mockAccountService.EXPECT().
			GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
			Return(
				[]accountservice.Order{
					{
						OrderId: "activeOrder123",
						PlanId:  2,
					},
				}, nil,
			)

		// Mock fetching plan by ids
		mockPlanService.EXPECT().GetPlansByIds(gomock.Any(), []uint64{2}).Return(
			[]planservice.PlanV2{{Id: 2, GeneralProviderAccess: true, FullUnlockAccess: true}}, nil,
		)

		// Mock fetching plan for active order
		mockPlanService.EXPECT().GetPlanById(gomock.Any(), int32(2)).Return(
			planservice.PlanV2{GeneralProviderAccess: true, FullUnlockAccess: true}, nil,
		)

		// Mock activating studies
		mockRecordService.EXPECT().
			PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "activeOrder123").
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyActive123",
						OrganizationID: organizationID,
					},
				}, nil,
			)

		// Mock GetProviderByLegacyId
		mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), organizationID).Return(
			orgs.Provider{
				Name: "mock-provider",
			}, nil,
		)

		response, err := s.PostActivateStudies(context.Background(), accountID, activationKey, IP, deviceID)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(response.UnlockedStudies))
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
		assert.EqualValues(
			t,
			events.RecordsActivated,
			mockCIOEventProducer.CallLogs[0]["eventName"],
		)
		assert.ElementsMatch(t, []int64{organizationID},
			mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"])
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
		assert.EqualValues(
			t,
			events.NewRecordAvailable,
			mockCIOEventProducer.CallLogs[1]["eventName"],
		)
		assert.True(
			t,
			mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
		)
		assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
		assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
	})

	t.Run("successful activation with active order - Core", func(t *testing.T) {
		s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
			t,
		)
		defer ctrl.Finish()
		accountID := "accountActive123"
		deviceID := "device123"
		activationKey := coreapi.ActivationKey{
			DateOfBirth: "********",
		}
		IP := "mock-IP"
		organizationID := int64(3)

		// Mock fetching account info
		mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
			&accountservice.Account{
				AccountId: accountID,
				Email:     "<EMAIL>",
				IsPassSet: true,
			}, nil,
		)

		// Mock fetching locked studies
		mockRecordService.EXPECT().
			GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyActive123",
						OrganizationID: organizationID,
						DicomPatientTags: recordservice.DicomPatientTags{
							PatientBirthDate: "********",
						},
						AvailabilityStatus: recordservice.NO_AVAILABILITY,
					},
				}, nil,
			)

		// Mock facility-funded map
		mockOrgService.EXPECT().
			IsFacilityFunded(gomock.Any(), uint(organizationID)).
			Return(false, nil)

		// Mock fetching active orders
		mockAccountService.EXPECT().
			GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
			Return(
				[]accountservice.Order{
					{
						OrderId: "activeOrder123",
						PlanId:  19,
					},
				}, nil,
			)

		// Mock fetching plan by ids
		mockPlanService.EXPECT().GetPlansByIds(gomock.Any(), []uint64{19}).Return(
			[]planservice.PlanV2{
				{Id: 19, GeneralProviderAccess: true, FullUnlockAccess: true},
			}, nil,
		)

		// Mock fetching plan for active order
		mockPlanService.EXPECT().GetPlanById(gomock.Any(), int32(19)).Return(
			planservice.PlanV2{GeneralProviderAccess: true, FullUnlockAccess: true}, nil,
		)

		// Mock activating studies
		mockRecordService.EXPECT().
			PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "activeOrder123").
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyActive123",
						OrganizationID: organizationID,
					},
				}, nil,
			)
		// Mock GetProviderByLegacyId
		mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), organizationID).Return(
			orgs.Provider{
				Name: "mock-provider",
			}, nil,
		)

		response, err := s.PostActivateStudies(context.Background(), accountID, activationKey, IP, deviceID)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(response.UnlockedStudies))
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
		assert.EqualValues(
			t,
			events.RecordsActivated,
			mockCIOEventProducer.CallLogs[0]["eventName"],
		)
		assert.ElementsMatch(t, []int64{organizationID},
			mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"])
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
		assert.EqualValues(
			t,
			events.NewRecordAvailable,
			mockCIOEventProducer.CallLogs[1]["eventName"],
		)
		assert.True(
			t,
			mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
		)
		assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
		assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
	})

	t.Run("successful activation with active order - Basic", func(t *testing.T) {
		s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
			t,
		)
		defer ctrl.Finish()
		accountID := "accountActive123"
		deviceID := "device123"
		activationKey := coreapi.ActivationKey{
			DateOfBirth: "********",
		}
		IP := "mock-IP"
		organizationID := int64(3)

		// Mock fetching account info
		mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
			&accountservice.Account{
				AccountId: accountID,
				Email:     "<EMAIL>",
				IsPassSet: true,
			}, nil,
		)

		// Mock fetching locked studies
		mockRecordService.EXPECT().
			GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyActive123",
						OrganizationID: organizationID,
						DicomPatientTags: recordservice.DicomPatientTags{
							PatientBirthDate: "********",
						},
						AvailabilityStatus: recordservice.NO_AVAILABILITY,
					},
				}, nil,
			)

		// Mock facility-funded map
		mockOrgService.EXPECT().
			IsFacilityFunded(gomock.Any(), uint(organizationID)).
			Return(false, nil)

		// Mock fetching active orders
		mockAccountService.EXPECT().
			GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
			Return(
				[]accountservice.Order{
					{
						OrderId: "activeOrder123",
						PlanId:  16,
					},
				}, nil,
			)

		// Mock fetching plan by ids
		mockPlanService.EXPECT().GetPlansByIds(gomock.Any(), []uint64{16}).Return(
			[]planservice.PlanV2{
				{Id: 16, GeneralProviderAccess: true, FullUnlockAccess: false},
			}, nil,
		)

		// Mock fetching plan for active order
		mockPlanService.EXPECT().GetPlanById(gomock.Any(), int32(16)).Return(
			planservice.PlanV2{GeneralProviderAccess: true, FullUnlockAccess: false}, nil,
		)

		// Mock activating studies
		mockRecordService.EXPECT().
			PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "activeOrder123").
			Return(
				[]recordservice.PatientStudy{
					{
						UUID:           "studyActive123",
						OrganizationID: organizationID,
					},
				}, nil,
			)

		// Mock GetProviderByLegacyId
		mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), organizationID).Return(
			orgs.Provider{
				Name: "mock-provider",
			}, nil,
		)

		response, err := s.PostActivateStudies(context.Background(), accountID, activationKey, IP, deviceID)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(response.UnlockedStudies))
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
		assert.EqualValues(
			t,
			events.RecordsActivated,
			mockCIOEventProducer.CallLogs[0]["eventName"],
		)
		assert.ElementsMatch(t, []int64{organizationID},
			mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"])
		assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
		assert.EqualValues(
			t,
			events.NewRecordAvailable,
			mockCIOEventProducer.CallLogs[1]["eventName"],
		)
		assert.True(
			t,
			mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
		)
		assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
		assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
		assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
		assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
	})

	t.Run(
		"successful activation with active order - multiple studies - same DOB - same provider",
		func(t *testing.T) {
			s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
				t,
			)
			defer ctrl.Finish()
			accountID := "accountActive123"
			deviceID := "device123"
			activationKey := coreapi.ActivationKey{
				DateOfBirth: "********",
			}
			IP := "mock-IP"
			organizationID := int64(3)

			// Mock fetching account info
			mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
				&accountservice.Account{
					AccountId: accountID,
					Email:     "<EMAIL>",
					IsPassSet: true,
				}, nil,
			)

			// Mock fetching locked studies
			mockRecordService.EXPECT().
				GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "studyActive123",
							OrganizationID: organizationID,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
						{
							UUID:           "studyActive1234",
							OrganizationID: organizationID,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
					}, nil,
				)

			// Mock facility-funded map
			mockOrgService.EXPECT().
				IsFacilityFunded(gomock.Any(), uint(organizationID)).
				Return(false, nil)

			// Mock fetching active orders
			mockAccountService.EXPECT().
				GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
				Return(
					[]accountservice.Order{
						{
							OrderId: "activeOrder123",
							PlanId:  16,
						},
					}, nil,
				)

			// Mock fetching plan by ids
			mockPlanService.EXPECT().GetPlansByIds(gomock.Any(), []uint64{16}).Return(
				[]planservice.PlanV2{
					{Id: 16, GeneralProviderAccess: true, FullUnlockAccess: false},
				}, nil,
			)

			// Mock fetching plan for active order
			mockPlanService.EXPECT().GetPlanById(gomock.Any(), int32(16)).Return(
				planservice.PlanV2{GeneralProviderAccess: true, FullUnlockAccess: false}, nil,
			)

			// Mock activating studies
			mockRecordService.EXPECT().
				PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "activeOrder123").
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "studyActive123",
							OrganizationID: organizationID,
						},
						{
							UUID:           "studyActive1234",
							OrganizationID: organizationID,
						},
					}, nil,
				)

			// Mock GetProviderByLegacyId
			mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), organizationID).Return(
				orgs.Provider{
					Name: "mock-provider",
				}, nil,
			)

			response, err := s.PostActivateStudies(
				context.Background(),
				accountID,
				activationKey,
				IP,
				deviceID,
			)
			assert.NoError(t, err)
			assert.Equal(t, 2, len(response.UnlockedStudies))
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
			assert.EqualValues(
				t,
				events.RecordsActivated,
				mockCIOEventProducer.CallLogs[0]["eventName"],
			)
			assert.ElementsMatch(
				t,
				[]int64{int64(organizationID)},
				mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"],
			)
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
			assert.EqualValues(
				t,
				events.NewRecordAvailable,
				mockCIOEventProducer.CallLogs[1]["eventName"],
			)
			assert.True(
				t,
				mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
			)
			assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
			assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
		},
	)

	t.Run(
		"successful activation with active order - multiple studies - same DOB - different provider",
		func(t *testing.T) {
			s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
				t,
			)
			defer ctrl.Finish()
			accountID := "accountActive123"
			deviceID := "device123"
			activationKey := coreapi.ActivationKey{
				DateOfBirth: "********",
			}
			IP := "mock-IP"

			// Mock fetching account info
			mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
				&accountservice.Account{
					AccountId: accountID,
					Email:     "<EMAIL>",
					IsPassSet: true,
				}, nil,
			)

			// Mock fetching locked studies
			mockRecordService.EXPECT().
				GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "studyActive123",
							OrganizationID: 123,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
						{
							UUID:           "studyActive1234",
							OrganizationID: 456,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
					}, nil,
				)

			// Mock facility-funded map
			mockOrgService.EXPECT().IsFacilityFunded(gomock.Any(), uint(123)).Return(false, nil)
			mockOrgService.EXPECT().IsFacilityFunded(gomock.Any(), uint(456)).Return(false, nil)

			// Mock fetching active orders
			mockAccountService.EXPECT().
				GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
				Return(
					[]accountservice.Order{
						{
							OrderId: "activeOrder123",
							PlanId:  16,
						},
					}, nil,
				)

			// Mock fetching plan by ids
			mockPlanService.EXPECT().GetPlansByIds(gomock.Any(), []uint64{16}).Return(
				[]planservice.PlanV2{
					{Id: 16, GeneralProviderAccess: true, FullUnlockAccess: false},
				}, nil,
			)

			// Mock fetching plan for active order
			mockPlanService.EXPECT().GetPlanById(gomock.Any(), int32(16)).Return(
				planservice.PlanV2{GeneralProviderAccess: true, FullUnlockAccess: false}, nil,
			)

			// Mock activating studies
			mockRecordService.EXPECT().
				PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "activeOrder123").
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "studyActive123",
							OrganizationID: 123,
						},
						{
							UUID:           "studyActive1234",
							OrganizationID: 456,
						},
					}, nil,
				)

			// Mock GetProviderByLegacyId
			mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), int64(123)).Return(
				orgs.Provider{
					Name: "mock-provider",
				}, nil,
			)
			mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), int64(456)).Return(
				orgs.Provider{
					Name: "mock-provider-2",
				}, nil,
			)

			response, err := s.PostActivateStudies(
				context.Background(),
				accountID,
				activationKey,
				IP,
				deviceID,
			)
			assert.NoError(t, err)
			assert.Equal(t, 2, len(response.UnlockedStudies))
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
			assert.EqualValues(
				t,
				events.RecordsActivated,
				mockCIOEventProducer.CallLogs[0]["eventName"],
			)
			assert.ElementsMatch(
				t,
				[]int64{123, 456},
				mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"],
			)
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
			assert.EqualValues(
				t,
				events.NewRecordAvailable,
				mockCIOEventProducer.CallLogs[1]["eventName"],
			)
			assert.True(
				t,
				mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
			)
			assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
			assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
		},
	)

	t.Run(
		"successful activation with active order - multiple studies - different DOB - different provider",
		func(t *testing.T) {
			s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmplitudeEventClient, ctrl := setupMocks(
				t,
			)
			defer ctrl.Finish()
			accountID := "accountActive123"
			deviceID := "device123"
			activationKey := coreapi.ActivationKey{
				DateOfBirth: "********",
			}
			IP := "mock-IP"

			// Mock fetching account info
			mockAccountService.EXPECT().GetAccountInfo(gomock.Any(), accountID).Return(
				&accountservice.Account{
					AccountId: accountID,
					Email:     "<EMAIL>",
					IsPassSet: true,
				}, nil,
			)

			// Mock fetching locked studies
			mockRecordService.EXPECT().
				GetStudies(mock.Anything, accountID, false, false, mock.Anything, mock.Anything).
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "studyActive123",
							OrganizationID: 3,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
						{
							UUID:           "studyActive1234",
							OrganizationID: 4,
							DicomPatientTags: recordservice.DicomPatientTags{
								PatientBirthDate: "********",
							},
							AvailabilityStatus: recordservice.NO_AVAILABILITY,
						},
					}, nil,
				)

			// Mock facility-funded map
			mockOrgService.EXPECT().IsFacilityFunded(gomock.Any(), uint(3)).Return(false, nil)

			// Mock fetching active orders
			mockAccountService.EXPECT().
				GetOrders(gomock.Any(), accountID, map[string]bool{"active": true}).
				Return(
					[]accountservice.Order{
						{
							OrderId: "activeOrder123",
							PlanId:  16,
						},
					}, nil,
				)

			// Mock fetching plan by ids
			mockPlanService.EXPECT().GetPlansByIds(gomock.Any(), []uint64{16}).Return(
				[]planservice.PlanV2{
					{Id: 16, GeneralProviderAccess: true, FullUnlockAccess: false},
				}, nil,
			)

			// Mock fetching plan for active order
			mockPlanService.EXPECT().GetPlanById(gomock.Any(), int32(16)).Return(
				planservice.PlanV2{GeneralProviderAccess: true, FullUnlockAccess: false}, nil,
			)

			// Mock activating studies
			mockRecordService.EXPECT().
				PostActivateStudies(mock.Anything, accountID, activationKey, mock.Anything, "activeOrder123").
				Return(
					[]recordservice.PatientStudy{
						{
							UUID:           "studyActive123",
							OrganizationID: 123,
						},
					}, nil,
				)

			// Mock GetProviderByLegacyId
			mockOrgService.EXPECT().GetProviderByLegacyId(gomock.Any(), int64(123)).Return(
				orgs.Provider{
					Name: "mock-provider",
				}, nil,
			)

			response, err := s.PostActivateStudies(
				context.Background(),
				accountID,
				activationKey,
				IP,
				deviceID,
			)
			assert.NoError(t, err)
			assert.Equal(t, 1, len(response.UnlockedStudies))
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[0]["acctId"])
			assert.EqualValues(
				t,
				events.RecordsActivated,
				mockCIOEventProducer.CallLogs[0]["eventName"],
			)
			assert.ElementsMatch(
				t,
				[]int64{123},
				mockCIOEventProducer.CallLogs[0]["eventAttributes"].(map[string]interface{})["orgIDs"],
			)
			assert.Equal(t, accountID, mockCIOEventProducer.CallLogs[1]["acctId"])
			assert.EqualValues(
				t,
				events.NewRecordAvailable,
				mockCIOEventProducer.CallLogs[1]["eventName"],
			)
			assert.True(
				t,
				mockCIOEventProducer.CallLogs[1]["eventAttributes"].(map[string]interface{})["activated"].(bool),
			)
			assert.Equal(t, "new record available", mockAmplitudeEventClient.EventLogs[0].EventType)
			assert.Equal(t, "new transfer available", mockAmplitudeEventClient.EventLogs[1].EventType)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[0].UserID)
			assert.Equal(t, accountID, mockAmplitudeEventClient.EventLogs[1].UserID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[0].DeviceID)
			assert.Equal(t, deviceID, mockAmplitudeEventClient.EventLogs[1].DeviceID)
		},
	)
}

func setupMocks(t *testing.T) (
	*CheckoutApiService,
	*mock_accountservice.MockAccountService,
	*mock_orgs.MockOrgService,
	*mock_plans.MockPlanService,
	*mockrecordservice.MockRecordServiceClientInterface,
	*mocks.MockCIOProducerData,
	*mocks.MockAmplitudeEventData,
	*gomock.Controller,
) {
	ctrl := gomock.NewController(t)
	mockAccountService := mock_accountservice.NewMockAccountService(ctrl)
	mockPlanService := mock_plans.NewMockPlanService(ctrl)
	mockOrgService := mock_orgs.NewMockOrgService(ctrl)
	mockRecordService := mockrecordservice.NewMockRecordServiceClientInterface(t)
	mockCIOEventProducer := &mocks.MockCIOProducerData{
		CallLogs: make([]map[string]any, 0),
	}
	mockAmpliEventClient := &mocks.MockAmplitudeEventData{
		EventLogs: make([]amplitude.Event, 0),
	}
	db := testutils.SetupTestDB(t)

	s := &CheckoutApiService{
		sqldb:                db,
		acctSvcUser:          mockAccountService,
		planSvc:              mockPlanService,
		orgSvc:               mockOrgService,
		recordSvcClient:      mockRecordService,
		cioEventProducer:     mockCIOEventProducer,
		amplitudeEventClient: mockAmpliEventClient,
	}

	return s, mockAccountService, mockOrgService, mockPlanService, mockRecordService, mockCIOEventProducer, mockAmpliEventClient, ctrl
}

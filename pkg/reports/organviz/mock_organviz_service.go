package organviz

import (
	"context"

	"github.com/samber/lo"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

type MockOrganvizService struct {
	EligibleExams        []string
	FetchedVizForReports []string
}

func (s *MockOrganvizService) IsExamEligible(ctx context.Context, exam coreapi.ExamRawBasic) (ExamEligibility, error) {
	if !lo.Contains(s.EligibleExams, exam.UUID) {
		return UNKNOWN, nil
	}
	return ELIGIBLE_FOR_MEDSAM, nil
}

func (s *MockOrganvizService) FetchVisualizationsForReport(ctx context.Context, reportId, accountId string, eligibility ExamEligibility, examId string) ([]byte, error) {
	s.FetchedVizForReports = append(s.FetchedVizForReports, reportId)
	return []byte{}, nil
}

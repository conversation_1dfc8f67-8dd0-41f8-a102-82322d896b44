//go:build integration
// +build integration

package topicapi_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz/topicapi"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/pubsub"
)

const (
	testServiceBusName = "sb-ph-qa"
)

func TestTriggersInferenceWhenReceivingMessage(t *testing.T) {
	// set up test data
	examUuid := "organviz_topicsub_test_exam_uuid"
	acctId := "organviz_test_account_id"
	db := testutils.SetupTestDB(t)
	organviz.CreateExamAndReport(t, db, reportId, examUuid, acctId)
	/// ensure we're working with exams that haven't been activated yet as that
	/// will mimic production
	_, err := db.Exec("UPDATE exams SET activated=0 WHERE uuid=?", examUuid)
	require.NoError(t, err)

	// create depedencies
	mockOviz := &organviz.MockOrganvizService{
		EligibleExams: []string{examUuid},
	}
	examService := newExamService(t, db)
	listener := newMockTopicListener()

	// start listening for messages
	l, err := topicapi.NewListener(
		mockOviz,
		examService,
		listener,
	)
	require.NoError(t, err)
	l.Start(context.Background())

	// send message
	listener.msgChan <- topicapi.Message{
		NewState: "CREATED",
		ExamUUID: examUuid,
	}
	// wait until the listener has finished processing the message
	<-listener.doneChan

	assert.Equal(t, []string{reportId}, mockOviz.FetchedVizForReports)
}

func newExamService(t *testing.T, db *sql.DB) exams.ExamServiceInterface {
	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examService := exams.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&exams.MockMigrationHelper{},
	)
	return examService
}

type mockTopicListener struct {
	msgChan  chan topicapi.Message
	doneChan chan topicapi.Message
}

func newMockTopicListener() *mockTopicListener {
	return &mockTopicListener{
		msgChan:  make(chan topicapi.Message),
		doneChan: make(chan topicapi.Message, 1000),
	}
}

func (m *mockTopicListener) Run(
	ctx context.Context,
	cb pubsub.MessageHandlerFn[topicapi.Message],
) error {
	for msg := range m.msgChan {
		err := cb(ctx, msg)
		m.doneChan <- msg
		if err != nil {
			return err
		}
	}
	close(m.doneChan)
	return nil
}

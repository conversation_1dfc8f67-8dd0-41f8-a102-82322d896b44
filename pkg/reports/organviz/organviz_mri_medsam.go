package organviz

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz/orientation"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type OrganVizMRIMedsam struct {
	riClient reportinsights.ReportInsights
}

type SequencePriority struct {
	Sequence string
	Priority float64
}

func NewOrganVizMRIMedsam(riClient reportinsights.ReportInsights) *OrganVizMRIMedsam {
	return &OrganVizMRIMedsam{
		riClient: riClient,
	}
}

func (s *OrganVizMRIMedsam) FetchVisualizations(ctx context.Context, reportId string, examId string) ([]byte, error) {
	query := map[string]string{"model": "mri_abd", "exam_id": examId}
	return s.riClient.GetOrganVisualization(ctx, reportId, query)
}

func isExamEligibleForMRI(exam coreapi.ExamRawBasic) (ExamEligibility, error) {
	age, err := patientAgeAtTimeOfExam(exam)
	if err != nil {
		return UNKNOWN, fmt.Errorf("unable to get patient age at time of exam: %v", err)
	}
	if age < 18 {
		return INELIGIBLE_AGE, nil
	}
	// explicitly exclude fetal scan
	examDesc := exam.Description
	if strings.Contains(examDesc, "fetal") {
		return INELIGIBLE_BODY_PART, nil
	}

	if !bodyPartMatches(exam, "chest", "abd") {
		return INELIGIBLE_BODY_PART, nil
	}
	return ELIGIBLE_FOR_MRI, nil
}

func getSeriesSequencePriority() []SequencePriority {
	return []SequencePriority{
		SequencePriority{"(?i)T1", 1},
		SequencePriority{"(?i)VIBE", 1},                    //T1
		SequencePriority{"(?i)LAVA", 1},                    //T1
		SequencePriority{"(?i)THRIVE", 1},                  //T1
		SequencePriority{`(?i)_W(\b|[^a-zA-Z0-9])`, 2},     //Water Only
		SequencePriority{`(?i)_Water(\b|[^a-zA-Z0-9])`, 2}, //Water Only
		SequencePriority{"(?i)T2", 3},
		SequencePriority{"(?i)HASTE", 3}, //T2
	}
}

// return list of series that has the same priority
func findHighestPrioritySeriesBySeriesDesc(seriesMetadata []*seriesData) []*seriesData {
	priorities := getSeriesSequencePriority()
	highestPriority := float64(^uint(0) >> 1)
	var bestSeries []*seriesData //it's possible that multiple series has the same priority sequence

	for _, sm := range seriesMetadata {
		desc := strings.ToUpper(sm.description)
		// it's rare but sometimes ABD exam may contain brain scan, so exclude
		if strings.Contains(desc, "BRAIN") {
			continue
		}

		// skipping DWI (Diffusion-weighted imaging) and ADC (Apparent diffusion coefficient) as the diffusion adds a lot of noise to image
		// skipping Subtraction Images as it's not ideal for segmentation
		// skipping Fat-Only as most organs we are targeting are water rich
		if strings.Contains(desc, "DWI") || strings.Contains(desc, "ADC") || strings.Contains(desc, "DIFFUSION") ||
			strings.Contains(desc, "_SUB") {
			continue
		}
		reF := regexp.MustCompile(`(?i)_F(\b|[^a-zA-Z0-9])`)
		reFat := regexp.MustCompile(`(?i)_Fat(\b|[^a-zA-Z0-9])`)
		if reF.MatchString(desc) || reFat.MatchString(desc) {
			continue
		}

		currPriority := 4.0
		for _, sp := range priorities {
			re := regexp.MustCompile(sp.Sequence)
			if re.MatchString(desc) {
				currPriority = sp.Priority
				break
			}
		}

		if strings.Contains(desc, "LOWER") {
			currPriority += 0.5 // series focus on LOWER abd should have lower priority than series focus on UPPER abd
		}

		if currPriority < highestPriority {
			highestPriority = currPriority
			bestSeries = bestSeries[:0] // clear the current slice
			bestSeries = append(bestSeries, sm)
		} else if currPriority == highestPriority {
			bestSeries = append(bestSeries, sm)
		}
	}
	return bestSeries
}

func isMRIScan(exam coreapi.ExamRawBasic) bool {
	haystack := strings.ToLower(exam.Modality)

	if exam.Modality == "" {
		haystack = strings.ToLower(exam.Description)
	}
	return strings.Contains(haystack, "mr") || strings.Contains(haystack, "mri")
}

func (s *Client) selectValidSeriesForMRI(ctx context.Context, examUuid string) (*string, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"exam_uuid": examUuid,
	})

	seriesMetadata, err := s.getObjectMetadataInEachSeriesByExam(ctx, examUuid)
	if err != nil {
		return nil, fmt.Errorf("failed getting series metadata in exam %v: %w", examUuid, err)
	}

	if seriesMetadata == nil {
		lg.Info("no valid series in exam")
		return nil, nil
	}

	filteredSeries := make([]*seriesData, 0)
	for _, sm := range seriesMetadata {
		descUpper := strings.ToUpper(sm.description)
		if sm.orientation != orientation.AXIAL && !strings.Contains(descUpper, "AX") {
			continue
		}

		if sm.sopClass == secondaryCaptureImageStorage {
			// Objects from "Secondary Capture Image Storage" are unlikely to be
			// scanned images, and more likely to be reports and other types of
			// data. Organviz only works on scanned images, so ignore any series
			// with this SOP class. See AIT-84
			continue
		}

		filteredSeries = append(filteredSeries, &sm)
	}

	if len(filteredSeries) == 0 {
		// no valid series found, but also no specific errors. there simply weren't any
		// valid series to select
		return nil, nil
	}

	eligibleSeries := findHighestPrioritySeriesBySeriesDesc(filteredSeries)
	eligibleSeriesIds := make([]string, 0)
	for _, ss := range eligibleSeries {
		eligibleSeriesIds = append(eligibleSeriesIds, ss.seriesUid)
	}
	if len(eligibleSeriesIds) == 0 {
		// no valid series found, but also no specific errors. there simply weren't any
		// valid series to select after checking series description
		return nil, nil
	}

	// To increase likelihood of giving the organviz model varied images to work
	// on we want to select the series with the most images in it
	err = s.sortSeriesByImgCountDesc(ctx, eligibleSeriesIds)
	if err != nil {
		return nil, fmt.Errorf("failed sorting series in exam %v: %w", examUuid, err)
	}

	bestSeries := eligibleSeriesIds[0]
	return &bestSeries, nil
}

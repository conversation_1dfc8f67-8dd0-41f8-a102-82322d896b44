package reportprocessor

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"

	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type RPClient struct {
	URL           string `json:"url"`
	User          string `json:"name"`
	Api<PERSON>ey        string `json:"api_key"`
	ApiKeySecName string `json:"api_key_sec_name"`
	authheader    string
	Client        *httpclient.Client
}

func NewClient(url string, user string, apikey string) RPClient {
	rp := RPClient{
		URL:    url,
		User:   user,
		ApiKey: apikey,
		Client: httpclient.NewHTTPClient(&http.Client{}, nil),
	}
	rp.setAuthValue()
	return rp
}

func (rp *RPClient) setAuthValue() {
	userKey := rp.User + ":" + rp.ApiKey
	rp.authheader = base64.StdEncoding.EncodeToString([]byte(userKey))
}

func (rp RPClient) GetReport(ctx context.Context, reportId string, accept string) ([]byte, error) {
	endpoint := fmt.Sprintf("%s/v1/reports/%s", rp.URL, reportId)

	return rp.getByteArrayRequest(ctx, endpoint, accept, make(map[string]string), nil, "GET")
}

func (rp RPClient) GetTaggedReport(
	ctx context.Context,
	reportId string,
	subdictionary string,
) ([]byte, error) {
	endpoint := fmt.Sprintf("%s/v1/reports/%s/taggedhtml", rp.URL, reportId)
	queryParams := map[string]string{
		"subdictionary": subdictionary,
	}

	return rp.getByteArrayRequest(ctx, endpoint, "", queryParams, nil, "GET")
}

func (rp RPClient) GetQuestions(ctx context.Context, reportId string) ([]byte, error) {
	endpoint := fmt.Sprintf("%s/v1/reports/%s/insights/questions", rp.URL, reportId)

	return rp.getByteArrayRequest(ctx, endpoint, "", make(map[string]string), nil, "GET")
}

func (rp RPClient) GetFollowup(ctx context.Context, reportId string) ([]byte, error) {
	endpoint := fmt.Sprintf("%s/v1/reports/%s/insights/followup", rp.URL, reportId)
	return rp.getByteArrayRequest(ctx, endpoint, "", make(map[string]string), nil, "GET")
}

func (rp RPClient) getByteArrayRequest(
	ctx context.Context,
	endpoint string,
	accept string,
	queryParams map[string]string,
	reqBody []byte,
	method string,
) ([]byte, error) {

	headers := map[string]string{
		"Content-type": "application/json",
	}
	if accept != "" {
		headers["Accept"] = accept
	}
	resp, status, err := rp.Client.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    method,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Basic,
		AuthKey:       rp.authheader,
		QueryParams:   queryParams,
		ReqBody:       reqBody,
		Headers:       headers,
		ExpectedCodes: []int{http.StatusNotFound},
	})
	if err != nil {
		return nil, fmt.Errorf("failed executing HTTP request: %w", err)
	}

	if status == http.StatusNotFound {
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, resp)
	if err != nil {
		logutils.DebugCtxLogger(ctx).WithError(err).Error("error reading response body")
		return nil, err
	}
	defer func() {
		err := resp.Close()
		if err != nil {
			logutils.DebugCtxLogger(ctx).WithError(err).Error("error closing response body")
		}
	}()
	return buf.Bytes(), err
}

func (rp *RPClient) GetReportExplanation(ctx context.Context, reportId string) ([]byte, error) {
	endpoint := fmt.Sprintf("%s/v1/reports/%s/insights/explanation", rp.URL, reportId)
	return rp.getByteArrayRequest(ctx, endpoint, "", make(map[string]string), nil, "GET")
}

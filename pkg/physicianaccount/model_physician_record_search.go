package physicianaccount

import (
	"time"
)

type PhysicianRecordSearchParameters struct {
	ProviderId int64                       `json:"providerId"`
	Query      RecordSearchQueryParameters `json:"query"`
}

func (p *PhysicianRecordSearchParameters) IsValid() bool {
	return p.ProviderId != 0 && p.Query.IsValid()
}

type RecordSearchQueryParameters struct {
	FirstName      string `json:"firstName"`
	LastName       string `json:"lastName"`
	DateOfBirth    string `json:"dateOfBirth"`              // date in format RFC3339 (yyyy-mm-dd)
	StudyStartDate string `json:"studyStartDate,omitempty"` // date in format RFC3339 (yyyy-mm-dd)
	StudyEndDate   string `json:"studyEndDate,omitempty"`   // date in format RFC3339 (yyyy-mm-dd)
}

func (p *RecordSearchQueryParameters) IsValid() bool {
	// has to have first name and last name
	if p.FirstName == "" || p.LastName == "" {
		return false
	}
	// date of birth needs to be date in format RFC3339
	if _, err := time.Parse("2006-01-02", p.DateOfBirth); err != nil {
		return false
	}
	// if study start date is set, it needs to be date in format RFC3339
	if p.StudyStartDate != "" {
		if _, err := time.Parse("2006-01-02", p.StudyStartDate); err != nil {
			return false
		}
	}
	// if study start date is set, it needs to be date in format RFC3339
	if p.StudyEndDate != "" {
		if _, err := time.Parse("2006-01-02", p.StudyEndDate); err != nil {
			return false
		}
	}
	return true
}

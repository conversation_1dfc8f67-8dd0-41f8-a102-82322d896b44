package physicianaccount_test

import (
	"bytes"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	mockphysicianaccount "gitlab.com/pockethealth/coreapi/generated/mocks/physicianaccount"
	mockrecordstreaming "gitlab.com/pockethealth/coreapi/generated/mocks/recordstreaming"
	"gitlab.com/pockethealth/coreapi/pkg/auth"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/physicianaccount"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/audit"
	phtestutil "gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

const (
	localClaimIp = "127.0.0.1"
)

type ControllerTestData struct {
	method               string
	url                  string
	body                 string // body as json string
	expectedStatus       int
	skipAuthorizedHeader bool
	// setup function can be used to set up mocks at the start of a test
	initMocks func(
		service *mockphysicianaccount.MockPhysicianAccountApiServicer,
		accountService *accountservice.AcctSvcMock,
		recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
	)
}

func TestGetPatients(t *testing.T) {
	testcases := map[string]ControllerTestData{
		"should return StatusOK on valid request": {
			method:         "GET",
			url:            "/v1/physician_accounts/patients",
			expectedStatus: http.StatusOK,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetShareMetadata(mock.Anything, mock.Anything).
					Return(nil, nil)
				recordStreamingService.EXPECT().
					GetShareMetadataForRecordStreamingStudies(mock.Anything, mock.Anything).
					Return(nil, nil)
			},
		},
		"should return StatusUnauthorized on invalid auth": {
			method:               "GET",
			url:                  "/v1/physician_accounts/patients",
			expectedStatus:       http.StatusUnauthorized,
			skipAuthorizedHeader: true,
		},
	}

	for description, testcase := range testcases {
		t.Run(description, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestPostSearch(t *testing.T) {
	testcases := map[string]ControllerTestData{
		"should return StatusOK on valid request": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusOK, // because service still returns error
			body: `{
				"providerId": 2000035,
				"query": {
						"firstName": "James",
						"lastName": "Shen",
						"dateOfBirth": "1990-01-01",
						"studyStartDate": "2020-10-28",
						"studyEndDate": "2023-12-31"
					}
				}`,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					SearchRecords(mock.Anything, mock.Anything, mock.Anything).
					Return("", nil)
			},
		},
		"should return StatusUnauthorized on invalid auth": {
			method:               "POST",
			url:                  "/v1/physician_accounts/search",
			expectedStatus:       http.StatusUnauthorized,
			skipAuthorizedHeader: true,
		},
		"should return StatusBadRequest on invalid request body": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body:           `{}`,
		},
		"should return StatusBadRequest on missing provider id": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"query": {
						"firstName": "James",
						"lastName": "Shen",
						"dateOfBirth": "1990-01-01"
					}
				}`,
		},
		"should return StatusBadRequest on missing query id": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035
				}`,
		},
		"should return StatusBadRequest on missing first name": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035,
				"query": {
						"lastName": "Shen",
						"dateOfBirth": "1990-01-01"
					}
				}`,
		},
		"should return StatusBadRequest on missing last name": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035,
				"query": {
						"firstName": "James",
						"dateOfBirth": "1990-01-01"
					}
				}`,
		},
		"should return StatusBadRequest on missing date of birth": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035,
				"query": {
						"firstName": "James",
						"lastName": "Shen"
					}
				}`,
		},
		"should return StatusBadRequest on invalid date format": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035,
				"query": {
					"firstName": "James",
					"lastName": "Shen",
					"dateOfBirth": "1990-01-01",
					"studyStartDate": "03/01/2000",
					"studyEndDate": "2023-12-31"
					}
				}`,
		},
	}

	for description, testcase := range testcases {
		t.Run(description, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestPostShareRequest(t *testing.T) {
	testcases := map[string]ControllerTestData{
		"should return StatusOK on valid request": {
			method:         "POST",
			url:            "/v1/physician_accounts/shares/request",
			expectedStatus: http.StatusOK, // because service still returns error
			body: `{
				"providerId": 2000035,
				"studyRequests": [
					{
						"patientId": "ABC",
						"studyUID": "GHI"
					},
					{
						"patientId": "JKL",
						"studyUID": "PQR"
					}
				]
				}`,

			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					RequestRecords(mock.Anything, mock.Anything, mock.Anything).
					Return(nil, nil)
			},
		},
		"should return StatusUnauthorized on invalid auth": {
			method:               "POST",
			url:                  "/v1/physician_accounts/shares/request",
			expectedStatus:       http.StatusUnauthorized,
			skipAuthorizedHeader: true,
		},
		"should return StatusBadRequest on invalid request body": {
			method:         "POST",
			url:            "/v1/physician_accounts/shares/request",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body:           `{}`,
		},
		"should return StatusBadRequest on missing provider id": {
			method:         "POST",
			url:            "/v1/physician_accounts/shares/request",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"studyRequests": [
					{
						"patientId": "ABC",
						"studyUID": "GHI"
					}
				]
				}`,
		},
		"should return StatusBadRequest on missing study": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035,
				"studyRequests": []
				}`,
		},
		"should return StatusBadRequest on invalid study requests": {
			method:         "POST",
			url:            "/v1/physician_accounts/search",
			expectedStatus: http.StatusBadRequest, // because service still returns error
			body: `{
				"providerId": 2000035,
				"studyRequests": [
					{
						"patientId": "ABC",
						"studyUID": "GHI"
					}
				]
				}`,
		},
	}

	for description, testcase := range testcases {
		t.Run(description, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestGetEUnityTokenForShare(t *testing.T) {
	studyUID := phtestutil.GenerateRandomString(t, 10)
	providerID := phtestutil.GenerateRandomInt64(t)
	url := fmt.Sprintf("/v1/physician_accounts/shares/%s/token", studyUID)
	urlWithProviderId := fmt.Sprintf("%s?providerId=%v", url, providerID)
	token := "dummyToken"

	testcases := map[string]ControllerTestData{
		"should return StatusUnauthorized on invalid auth": {
			method:               "GET",
			url:                  urlWithProviderId,
			expectedStatus:       http.StatusUnauthorized,
			skipAuthorizedHeader: true,
		},
		"should return StatusOK on valid request": {
			method:         "GET",
			url:            urlWithProviderId,
			expectedStatus: http.StatusOK,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetEUnityTokenForShare(mock.Anything, mock.Anything, studyUID).
					Return(token, nil)
			},
		},
		"should return StatusOK on valid request for record streaming study": {
			method:         "GET",
			url:            urlWithProviderId,
			expectedStatus: http.StatusOK,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetEUnityTokenForShare(mock.Anything, mock.Anything, studyUID).
					Return("", errors.New(errormsgs.ERR_NOT_AUTHORIZED))
				recordStreamingService.EXPECT().
					CreateEUnityAccessTokenForStudy(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return(token, nil)
			},
		},
		"should return StatusUnauthorized if no provider ID was given and no share exists": {
			method:         "GET",
			url:            url,
			expectedStatus: http.StatusUnauthorized, // returns unauthorized when no study was found
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetEUnityTokenForShare(mock.Anything, mock.Anything, studyUID).
					Return("", errors.New(errormsgs.ERR_NOT_AUTHORIZED))
			},
		},
		"should return StatusUnauthorized if physician does not have access to share or study": {
			method:         "GET",
			url:            urlWithProviderId,
			expectedStatus: http.StatusUnauthorized,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetEUnityTokenForShare(mock.Anything, mock.Anything, studyUID).
					Return("", errors.New(errormsgs.ERR_NOT_AUTHORIZED))
				recordStreamingService.EXPECT().
					CreateEUnityAccessTokenForStudy(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return("", errors.New(errormsgs.ERR_NOT_AUTHORIZED))
			},
		},
		"should return StatusInternalServerError on unexpected error when trying to create token for share": {
			method:         "GET",
			url:            url,
			expectedStatus: http.StatusInternalServerError,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetEUnityTokenForShare(mock.Anything, mock.Anything, studyUID).
					Return("", errors.New("unexpected error"))
			},
		},
		"should return StatusInternalServerError on unexpected error when trying to create token for record streaming study": {
			method:         "GET",
			url:            urlWithProviderId,
			expectedStatus: http.StatusInternalServerError,
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					GetEUnityTokenForShare(mock.Anything, mock.Anything, studyUID).
					Return("", errors.New(errormsgs.ERR_NOT_AUTHORIZED))
				recordStreamingService.EXPECT().
					CreateEUnityAccessTokenForStudy(mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Return("", errors.New("unexpected error"))
			},
		},
	}

	for description, testcase := range testcases {
		t.Run(description, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func TestPostPhysicianLicense(t *testing.T) {
	physicianId := phtestutil.GenerateRandomInt64(t)
	licenseType := phtestutil.GenerateRandomString(t, 2)
	licenseNo := phtestutil.GenerateRandomString(t, 5)
	stateOrProvince := phtestutil.GenerateRandomString(t, 2)
	expiryDate := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)

	expectedLicenseRequest := accountservice.PhysicianLicenceRequest{
		LicenceType:     licenseType,
		LicenseNo:       licenseNo,
		StateOrProvince: stateOrProvince,
		ExpiryDate:      expiryDate,
	}

	testcases := map[string]ControllerTestData{
		"should return StatusOK on valid request": {
			method: "POST",
			url: fmt.Sprintf(
				"/v1/physician_accounts/physicians/%d/licenses",
				physicianId,
			),
			expectedStatus: http.StatusOK,
			body: fmt.Sprintf(`{
				"licenseType": "%s",
				"licenseNo": "%s",
				"stateOrProvince": "%s",
				"expiryDate": "%s"
			}`, licenseType, licenseNo, stateOrProvince, expiryDate.Format(time.RFC3339)),
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					PostPhysicianLicense(
						mock.Anything,
						mock.Anything,
						fmt.Sprintf("%d", physicianId),
						mock.MatchedBy(func(req accountservice.PhysicianLicenceRequest) bool {
							return assert.ObjectsAreEqual(expectedLicenseRequest, req)
						}),
					).
					Return(nil)
			},
		},
		"should return StatusUnauthorized on invalid auth": {
			method: "POST",
			url: fmt.Sprintf(
				"/v1/physician_accounts/physicians/%d/licenses",
				physicianId,
			),
			expectedStatus:       http.StatusUnauthorized,
			skipAuthorizedHeader: true,
		},
		"should return StatusBadRequest on invalid request body": {
			method: "POST",
			url: fmt.Sprintf(
				"/v1/physician_accounts/physicians/%d/licenses",
				physicianId,
			),
			expectedStatus: http.StatusBadRequest,
			body:           `{invalid json}`,
		},
		"should return StatusBadRequest when service returns error": {
			method: "POST",
			url: fmt.Sprintf(
				"/v1/physician_accounts/physicians/%d/licenses",
				physicianId,
			),
			expectedStatus: http.StatusBadRequest,
			body: fmt.Sprintf(`{
				"licenseType": "%s",
				"licenseNo": "%s",
				"stateOrProvince": "%s",
				"expiryDate": "%s"
			}`, licenseType, licenseNo, stateOrProvince, expiryDate.Format(time.RFC3339)),
			initMocks: func(
				service *mockphysicianaccount.MockPhysicianAccountApiServicer,
				accountService *accountservice.AcctSvcMock,
				recordStreamingService *mockrecordstreaming.MockRecordStreamingServicer,
			) {
				service.EXPECT().
					PostPhysicianLicense(
						mock.Anything,
						mock.Anything,
						fmt.Sprintf("%d", physicianId),
						mock.MatchedBy(func(req accountservice.PhysicianLicenceRequest) bool {
							return assert.ObjectsAreEqual(expectedLicenseRequest, req)
						}),
					).
					Return(errors.New("service error"))
			},
		},
	}

	for description, testcase := range testcases {
		t.Run(description, func(t *testing.T) {
			runControllerTest(t, testcase)
		})
	}
}

func setupController(t *testing.T) (
	coreapi.Router,
	*mockphysicianaccount.MockPhysicianAccountApiServicer,
	*accountservice.AcctSvcMock,
	*mockrecordstreaming.MockRecordStreamingServicer,
) {
	service := mockphysicianaccount.NewMockPhysicianAccountApiServicer(t)
	accountService := &accountservice.AcctSvcMock{}
	recordStreamingService := mockrecordstreaming.NewMockRecordStreamingServicer(t)
	controller := physicianaccount.NewPrivatePhysicianAccountApiController(
		service,
		accountService,
		recordStreamingService,
		&audit.AuditLogServiceMock{},
	)

	return controller, service, accountService, recordStreamingService
}

func runControllerTest(t *testing.T, data ControllerTestData) {
	// init controller and services
	controller, serivce, accountService, recordStreamingService := setupController(t)
	// call setup function
	if data.initMocks != nil {
		data.initMocks(serivce, accountService, recordStreamingService)
	}

	// set up controller
	router, err := coreapi.NewRouter(controller)
	if err != nil {
		t.Fatal(err)
	}

	// create request body
	buf := bytes.NewBuffer([]byte(data.body))

	// create request
	request, err := http.NewRequest(data.method, data.url, buf)
	if err != nil {
		t.Fatal(err)
	}

	// add auth header
	if !data.skipAuthorizedHeader {
		token := auth.MakeAccountAuthToken("2Xzj6jbsqdRaQYmqu0ynoebKVFw", localClaimIp)
		request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	// set up response recorder
	rr := httptest.NewRecorder()

	// process request
	router.ServeHTTP(rr, request)

	// check status code
	assert.Equal(t, data.expectedStatus, rr.Code)
}

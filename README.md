# Core API

![pipeline](https://gitlab.com/pockethealth/coreapi/badges/master/pipeline.svg)
![coverage](https://gitlab.com/pockethealth/coreapi/badges/master/coverage.svg)

Quick links:

- [🏥 Core API Health Metrics](https://portal.azure.com/#@adminmypockethealth.onmicrosoft.com/dashboard/arm/subscriptions/d12085a7-7fc3-449f-859f-40cefd7dd709/resourcegroups/dashboards/providers/microsoft.portal/dashboards/f3a25801-f1db-426d-b0a6-a207227920c5)
- [🏥 Core API Metrics](https://portal.azure.com/#@adminmypockethealth.onmicrosoft.com/dashboard/arm/subscriptions/d12085a7-7fc3-449f-859f-40cefd7dd709/resourcegroups/dashboards/providers/microsoft.portal/dashboards/8782eb95-4c17-43ab-904c-0d2e1b68d9c0)
- [🏥 Core API US West Metrics](https://portal.azure.com/#@adminmypockethealth.onmicrosoft.com/dashboard/arm/subscriptions/d12085a7-7fc3-449f-859f-40cefd7dd709/resourcegroups/dashboards/providers/microsoft.portal/dashboards/891e5490-3912-4b0a-8e08-5ad224fc1742)

Core API serves as a gateway for other internal services. It is currently in an unclear state in that it is not very well-defined what the service is responsible for and what it is not.

For the most part, it currently does these things:

- Write to and Reads from the `pockethealth` database
- Acts as a gateway for internal services so that endpoints can be called from the frontend
- Handles most business logic that has not been pulled into a separate service

## Quick Start/TLDR

Install Go 1.24, Gosec and Docker.

Start service connected to QA db: `make run_docker_qa`.

After that command, `make clean` to reset everything (doesn't delete Docker images from your registry).

Lunch N Learn rundown of core API structure, testing, CI found [here](https://drive.google.com/file/d/16FxZnibXUNNC6yvUrYKqFj_BHTfr4Rg8/view?usp=sharing)

## Overview

The `Makefile` provides the entry point for most use cases of this package. The
recommended approach for running this service is within a Docker container and
directions in this doc will be oriented for that usage.

- To build Docker image: `make docker`

The following can be used to conveniently build and launch in Docker:

- To launch service in Docker: `make run_docker_qa`

To reset everything to initial state (including tidying any Docker-related resources): `make clean`

## Development

The repo contains the folder required to run the debugger in VSCode. Edit it to point to your coreapi folder, and hit F5 in VSCode to debug.

You can debug in the QA or DEV environment, just change the environment variable in launch.json.

The dev environment will point to local resources (mysql - see next section), and the qa environment points to the qa dbs.

To work with the openapi spec `redocly/cli` is used to generate the combined OpenAPI spec.
Install with `npm install @redocly/cli -g`.

## Testing

To setup for using anything that requires specific command line tools (ie, pdf generation, image conversion), install the following:

- dcmtk - install with `brew install dcmtk`
- gdcm - install with `brew install gdcm`
- imagemagick - install with `brew install imagemagick`

To setup full integration testing, install the following:

- docker compose
- mysql-client

Then unzip the file in apptest/sql/Dump.sql.zip

- these are zipped because they are large and the pipelines don't like that, but they're only used for local development anyway.

There are some unit and integ tests, but coverage is poor. Add tests when you add code!!

- To build and run unit tests: `make build`
- To run keyvault integration tests on a dev machine: `make integ`
  - In order to enable Intellisense on your integ files, open VS code settings, set the "Go: Build Tags" to include "integration": ![go build tag setting](assets/dev/vs-code-setting-gobuildtag.png)
  - Run specific packages' integration tests: `PKGS=./pkg/somepackage/... make integ`
- To run full integration tests on a dev machine: `make docker` then `make apptest`

### Test Coverage

This project contains some useful code coverage targets. For various reasons, code coverage does not include apptests yet.

The goal should be to **not decrease** code coverage.

To open a useful view that shows the coverage per file, run:

```sh
make coverage_html
```

This project leverages `gocover-cobertura` to generate an xml coverage file that Gitlab can read.

### Apptest

Before running apptests, build [reportprocessor](https://gitlab.com/pockethealth/reportprocessor) locally using `make docker`. If you don't have this image, running apptests will not work.

`make apptest` spins up a local mysql service on a network called apptest_apptest, starts a docker instance of core API, then runs go test scripts that act as an HTTP client to do full integration tests with dbs. These are also run every time the service is deployed to qa.

To run the apptests against our QA services and databases, use `make apptest_qa_local`.

To use the local mysql db without running tests, run `make start_dc`. This will create the service and network for you, then you can run `make run_docker_dev` to connect to it. To tear down these resources, run `make stop_dc`.

## Gosec

Gosec is a tool that checks for common security vulnerabilities in go code.

Install gosec: `go get github.com/securego/gosec/v2/cmd/gosec`.

The CI build will only succeed if Gosec reports no issues. You can run `make gosec` to generate a Gosec report without building.
- Run Gosec for specific packages: `PKGS=./pkg/somepackage/... make gosec`


Ways to resolve gosec issues:

- Fix the problem (e.g. if Gosec reports unhandled errors, add error handlers)
- If you have confirmed there is no vulnerability, you can annotate a statement with a `#nosec` comment to make Gosec ignore it. See <https://github.com/securego/gosec/blob/master/README.md> for usage examples.
- If we want to ignore all issues of a certain type, add the rule to the gosec -exclude argument in the Makefile.

## DB Migrations

Note: This service reads and writes from tables that are also used by other services. When running migrations ensure that other services are not affected.

goose is used for mysql database migrations.
To setup locally: `$ go install github.com/pressly/goose/v3/cmd/goose@latest`
(`brew install goose` is also available.)

To create a new migration, run
`make migration NAME=my_migration`
This will create a file in /migrations. Edit this file - after the up StatementBegin, add the SQL to perform the migration.
If applicable, under the goose down section, add the SQL to revert the migration.

The following make commands upgrade/downgrade the db (apply the migrations, or revert the migrations) or check the migration status:
`make db_up CONNSTR='db_name:<pw>.....'`
`make db_down CONNSTR='db_name:<pw>.....'`
`make db_status CONNSTR='db_name:<pw>.....'`

The same commands suffixed with `_prod` need to be used for updating prod dbs (requires an extra command)
